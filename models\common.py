import torch
from torch import nn
import torch.nn.functional as F

__all__ = [
    "FeedForwardNet",
    "FeatAttention",
    "WeightedAggregator",
]


class FeedForwardNet(nn.Module):
    """A tiny MLP used repeatedly inside all models.

    It consists of *n_layers* linear layers with PReLU + Dropout (except for
    the last layer).  Xavier initialisation with ReLU gain is employed so the
    behaviour is identical to the previous definition copied in every model
    file.
    """

    def __init__(self, in_feats: int, hidden: int, out_feats: int, n_layers: int, dropout: float):
        super().__init__()
        self.layers = nn.ModuleList()
        self.n_layers = n_layers
        if n_layers == 1:
            self.layers.append(nn.Linear(in_feats, out_feats))
        else:
            self.layers.append(nn.Linear(in_feats, hidden))
            for _ in range(n_layers - 2):
                self.layers.append(nn.Linear(hidden, hidden))
            self.layers.append(nn.Linear(hidden, out_feats))
        if n_layers > 1:
            self.prelu = nn.PReLU()
            self.dropout = nn.Dropout(dropout)
        self._reset_parameters()

    # ---------------------------------------------------------------------
    # Utilities
    # ---------------------------------------------------------------------
    def _reset_parameters(self):
        gain = nn.init.calculate_gain("relu")
        for layer in self.layers:
            nn.init.xavier_uniform_(layer.weight, gain=gain)
            nn.init.zeros_(layer.bias)

    # ---------------------------------------------------------------------
    # Forward
    # ---------------------------------------------------------------------
    def forward(self, x: torch.Tensor) -> torch.Tensor:  # noqa: D401
        """Compute FFN output."""
        for i, layer in enumerate(self.layers):
            x = layer(x)
            if i < self.n_layers - 1:
                x = self.dropout(self.prelu(x))
        return x


class FeatAttention(nn.Module):
    """Hop-level feature attention shared by RGAT/RGSN.

    It follows the identical logic previously defined multiple times: a simple
    GAT-style additive attention between *L* and *R* learnable vectors.
    """

    def __init__(self, feat_dim: int, num_heads: int, attn_drop: float = 0.0, negative_slope: float = 0.2):
        super().__init__()
        self.hop_attn_l = nn.Parameter(torch.FloatTensor(1, num_heads, feat_dim))
        self.hop_attn_r = nn.Parameter(torch.FloatTensor(1, num_heads, feat_dim))
        self.attn_dropout = nn.Dropout(attn_drop)
        self.leaky_relu = nn.LeakyReLU(negative_slope)
        self._reset_parameters()

    def _reset_parameters(self):
        gain = nn.init.calculate_gain("relu")
        nn.init.xavier_normal_(self.hop_attn_l, gain=gain)
        nn.init.xavier_normal_(self.hop_attn_r, gain=gain)

    def forward(self, feats):
        astack_l = [(feat * self.hop_attn_l).sum(dim=-1).unsqueeze(-1) for feat in feats]
        a_r = (feats[0] * self.hop_attn_r).sum(dim=-1).unsqueeze(-1)
        astack = torch.cat([(a_l + a_r).unsqueeze(-1) for a_l in astack_l], dim=-1)
        a = self.leaky_relu(astack)
        a = F.softmax(a, dim=-1)
        a = self.attn_dropout(a)
        out = 0
        for i in range(a.shape[-1]):
            out += feats[i] * a[..., i]
        return out


class WeightedAggregator(nn.Module):
    """Hop-wise weighted aggregator originally copied inside model.py."""

    def __init__(self, num_feats: int, in_feats: int, num_hops: int):
        super().__init__()
        self.agg_feats = nn.ParameterList()
        for _ in range(num_hops):
            p = nn.Parameter(torch.Tensor(num_feats, in_feats))
            nn.init.xavier_uniform_(p)
            self.agg_feats.append(p)

    def forward(self, feats):
        new_feats = []
        for feat, weight in zip(feats, self.agg_feats):
            new_feats.append((feat * weight.unsqueeze(0)).sum(dim=1).squeeze())
        return new_feats 