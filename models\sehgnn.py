import torch
from torch import nn
import torch.nn.functional as F
import dgl
import dgl.function
from .common import FeedForwardNet
import math
from typing import List, Dict, Tuple, Optional


class MultiHopProcessor(nn.Module):
    """Processes multi-hop features with individual transformations."""
    
    def __init__(self, in_feats: int, hidden: int, num_hops: int, dropout: float = 0.1):
        super().__init__()
        self.num_hops = num_hops
        self.hidden = hidden
        
        # Individual processors for each hop
        self.hop_processors = nn.ModuleList([
            FeedForwardNet(in_feats, hidden, hidden, 2, dropout) 
            for _ in range(num_hops)
        ])
        
        # Hop-specific batch normalization
        self.hop_norms = nn.ModuleList([
            nn.BatchNorm1d(hidden) for _ in range(num_hops)
        ])
        
        # Learnable hop embeddings
        self.hop_embeddings = nn.Embedding(num_hops, hidden)
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, hop_features: List[torch.Tensor]) -> List[torch.Tensor]:
        """Process each hop feature individually."""
        processed_features = []
        
        for hop_idx, feat in enumerate(hop_features):
            # Apply hop-specific processing
            h = self.hop_processors[hop_idx](self.dropout(feat))
            
            # Add hop embedding
            hop_emb = self.hop_embeddings(
                torch.full((feat.size(0),), hop_idx, dtype=torch.long, device=feat.device)
            )
            h = h + hop_emb
            
            # Apply normalization
            h = self.hop_norms[hop_idx](h)
            
            processed_features.append(h)
        
        return processed_features


class SelfEnsembleModule(nn.Module):
    """Self-ensemble module with multiple prediction heads."""
    
    def __init__(self, hidden: int, out_feats: int, num_heads: int = 3, dropout: float = 0.1):
        super().__init__()
        self.num_heads = num_heads
        self.hidden = hidden
        self.out_feats = out_feats
        
        # Multiple prediction heads for ensemble
        self.prediction_heads = nn.ModuleList([
            nn.Sequential(
                FeedForwardNet(hidden, hidden, hidden, 2, dropout),
                nn.Linear(hidden, out_feats)
            ) for _ in range(num_heads)
        ])
        
        # Ensemble weights
        self.ensemble_weights = nn.Parameter(torch.ones(num_heads) / num_heads)
        
        # Temperature for distillation
        self.temperature = nn.Parameter(torch.tensor(4.0))
        
    def forward(self, features: torch.Tensor, return_individual: bool = False):
        """
        Forward pass with ensemble prediction.
        
        Args:
            features: Input features [N, hidden]
            return_individual: Whether to return individual head predictions
            
        Returns:
            Ensemble prediction and optionally individual predictions
        """
        individual_preds = []
        
        # Get predictions from each head
        for head in self.prediction_heads:
            pred = head(features)
            individual_preds.append(pred)
        
        # Ensemble prediction with learned weights
        weights = F.softmax(self.ensemble_weights, dim=0)
        ensemble_pred = sum(w * pred for w, pred in zip(weights, individual_preds))
        
        if return_individual:
            return ensemble_pred, individual_preds
        else:
            return ensemble_pred
    
    def compute_distillation_loss(self, features: torch.Tensor, target_logits: torch.Tensor) -> torch.Tensor:
        """Compute self-distillation loss between ensemble and individual heads."""
        ensemble_pred, individual_preds = self.forward(features, return_individual=True)
        
        # Soft targets from ensemble (teacher)
        soft_targets = F.softmax(ensemble_pred / self.temperature, dim=-1)
        
        # Distillation loss for each head (student)
        distill_losses = []
        for pred in individual_preds:
            student_logits = pred / self.temperature
            distill_loss = F.kl_div(
                F.log_softmax(student_logits, dim=-1),
                soft_targets,
                reduction='batchmean'
            )
            distill_losses.append(distill_loss)
        
        return sum(distill_losses) / len(distill_losses)


class AdaptiveFeatureFusion(nn.Module):
    """Adaptive fusion of multi-hop features with attention mechanism."""
    
    def __init__(self, hidden: int, num_hops: int, num_heads: int = 4):
        super().__init__()
        self.hidden = hidden
        self.num_hops = num_hops
        self.num_heads = num_heads
        
        # Multi-head attention for feature fusion
        self.attention = nn.MultiheadAttention(
            hidden, num_heads, dropout=0.1, batch_first=True
        )
        
        # Learnable query for global context
        self.global_query = nn.Parameter(torch.randn(1, 1, hidden))
        
        # Feature importance weights
        self.importance_weights = nn.Parameter(torch.ones(num_hops))
        
        # Output projection
        self.output_proj = nn.Linear(hidden, hidden)
        
    def forward(self, hop_features: List[torch.Tensor]) -> torch.Tensor:
        """Fuse multi-hop features adaptively."""
        batch_size = hop_features[0].size(0)
        
        # Stack hop features
        stacked_features = torch.stack(hop_features, dim=1)  # [N, num_hops, hidden]
        
        # Expand global query for batch
        global_query = self.global_query.expand(batch_size, -1, -1)  # [N, 1, hidden]
        
        # Apply attention with global query
        attended_features, attention_weights = self.attention(
            global_query, stacked_features, stacked_features
        )
        
        # Weighted combination with importance weights
        importance = F.softmax(self.importance_weights, dim=0)
        weighted_features = stacked_features * importance.view(1, -1, 1)
        
        # Combine attended and weighted features
        fused_features = attended_features.squeeze(1) + weighted_features.mean(dim=1)
        
        return self.output_proj(fused_features)


class ProgressiveTraining(nn.Module):
    """Progressive training module for curriculum learning."""
    
    def __init__(self, num_hops: int):
        super().__init__()
        self.num_hops = num_hops
        self.current_stage = 0
        self.stage_weights = nn.Parameter(torch.ones(num_hops))
        
    def get_active_hops(self, epoch: int, total_epochs: int) -> List[int]:
        """Get active hops based on training progress."""
        # Progressive curriculum: start with 1-hop, gradually add more
        progress = epoch / total_epochs
        active_hops = min(self.num_hops, max(1, int(progress * self.num_hops) + 1))
        return list(range(active_hops))
    
    def get_hop_weights(self, active_hops: List[int]) -> torch.Tensor:
        """Get weights for active hops."""
        weights = torch.zeros(self.num_hops, device=self.stage_weights.device)
        if active_hops:
            active_weights = F.softmax(self.stage_weights[:len(active_hops)], dim=0)
            for i, hop_idx in enumerate(active_hops):
                weights[hop_idx] = active_weights[i]
        return weights


class SeHGNN(nn.Module):
    """Complete Self-ensemble Heterogeneous Graph Neural Network.
    
    This implementation includes:
    1. Multi-hop feature processing with individual transformations
    2. Self-distillation mechanisms with multiple prediction heads
    3. Ensemble learning with learnable combination weights
    4. Progressive training with curriculum learning
    5. Adaptive feature fusion using attention mechanisms
    6. Proper normalization and regularization techniques
    """

    def __init__(
        self, 
        in_feats: int, 
        hidden: int, 
        out_feats: int, 
        num_hops: int, 
        dropout: float = 0.5,
        num_ensemble_heads: int = 3,
        use_progressive_training: bool = True,
        use_self_distillation: bool = True,
        distillation_weight: float = 0.5,
        temperature: float = 4.0
    ):
        super().__init__()
        self.num_hops = num_hops
        self.hidden = hidden
        self.out_feats = out_feats
        self.use_progressive_training = use_progressive_training
        self.use_self_distillation = use_self_distillation
        self.distillation_weight = distillation_weight
        
        # Multi-hop feature processor
        self.hop_processor = MultiHopProcessor(in_feats, hidden, num_hops, dropout)
        
        # Adaptive feature fusion
        self.feature_fusion = AdaptiveFeatureFusion(hidden, num_hops)
        
        # Self-ensemble module
        self.ensemble_module = SelfEnsembleModule(
            hidden, out_feats, num_ensemble_heads, dropout
        )
        
        # Progressive training module
        if use_progressive_training:
            self.progressive_trainer = ProgressiveTraining(num_hops)
        
        # Additional processing layers
        self.pre_ensemble_layers = nn.Sequential(
            FeedForwardNet(hidden, hidden, hidden, 2, dropout),
            nn.LayerNorm(hidden),
            nn.Dropout(dropout)
        )
        
        # Regularization
        self.dropout = nn.Dropout(dropout)
        self.input_dropout = nn.Dropout(dropout * 0.5)
        
        self.reset_parameters()

    def reset_parameters(self):
        """Initialize model parameters."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Parameter):
                nn.init.xavier_uniform_(module)

    def forward(self, feats: List[torch.Tensor], epoch: int = None, total_epochs: int = None, 
                return_individual: bool = False):
        """
        Forward pass of SeHGNN.
        
        Args:
            feats: List of multi-hop features
            epoch: Current training epoch (for progressive training)
            total_epochs: Total training epochs (for progressive training)
            return_individual: Whether to return individual head predictions
            
        Returns:
            Ensemble prediction and optionally individual predictions
        """
        assert len(feats) == self.num_hops, f"Expected {self.num_hops} hop features, got {len(feats)}"
        
        # Apply input dropout
        feats = [self.input_dropout(f) for f in feats]
        
        # Progressive training: select active hops
        if self.use_progressive_training and epoch is not None and total_epochs is not None:
            active_hops = self.progressive_trainer.get_active_hops(epoch, total_epochs)
            hop_weights = self.progressive_trainer.get_hop_weights(active_hops)
            
            # Weight the features based on progressive curriculum
            weighted_feats = []
            for i, feat in enumerate(feats):
                if i < len(hop_weights):
                    weighted_feats.append(feat * hop_weights[i])
                else:
                    weighted_feats.append(feat * 0.0)  # Inactive hops
            feats = weighted_feats
        
        # Process multi-hop features individually
        processed_feats = self.hop_processor(feats)
        
        # Adaptive feature fusion
        fused_features = self.feature_fusion(processed_feats)
        
        # Pre-ensemble processing
        h = self.pre_ensemble_layers(fused_features)
        
        # Self-ensemble prediction
        if return_individual or self.use_self_distillation:
            ensemble_pred, individual_preds = self.ensemble_module(h, return_individual=True)
            
            if return_individual:
                return ensemble_pred, individual_preds
            else:
                return ensemble_pred
        else:
            return self.ensemble_module(h, return_individual=False)

    def compute_loss(self, feats: List[torch.Tensor], targets: torch.Tensor,
                    epoch: int = None, total_epochs: int = None) -> Dict[str, torch.Tensor]:
        """
        Compute total loss including self-distillation.

        Args:
            feats: List of multi-hop features
            targets: Ground truth targets
            epoch: Current training epoch
            total_epochs: Total training epochs

        Returns:
            Dictionary of losses
        """
        # Forward pass
        ensemble_pred, individual_preds = self.forward(
            feats, epoch, total_epochs, return_individual=True
        )

        # Primary classification loss
        primary_loss = F.cross_entropy(ensemble_pred, targets)

        losses = {'primary_loss': primary_loss}

        # Self-distillation loss
        if self.use_self_distillation:
            distill_loss = self.ensemble_module.compute_distillation_loss(
                self.pre_ensemble_layers(self.feature_fusion(self.hop_processor(feats))),
                ensemble_pred.detach()
            )
            losses['distillation_loss'] = distill_loss

            # Total loss
            total_loss = primary_loss + self.distillation_weight * distill_loss
            losses['total_loss'] = total_loss
        else:
            losses['total_loss'] = primary_loss

        return losses

    def get_ensemble_weights(self) -> torch.Tensor:
        """Get current ensemble weights."""
        return F.softmax(self.ensemble_module.ensemble_weights, dim=0)

    def get_hop_importance(self) -> torch.Tensor:
        """Get current hop importance weights."""
        return F.softmax(self.feature_fusion.importance_weights, dim=0)

    def get_attention_weights(self, feats: List[torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Get attention weights for interpretability."""
        attention_weights = {}

        # Set model to evaluation mode
        was_training = self.training
        self.eval()

        with torch.no_grad():
            # Process features
            feats = [self.input_dropout(f) for f in feats]
            processed_feats = self.hop_processor(feats)

            # Get feature fusion attention weights
            batch_size = processed_feats[0].size(0)
            stacked_features = torch.stack(processed_feats, dim=1)
            global_query = self.feature_fusion.global_query.expand(batch_size, -1, -1)

            _, fusion_weights = self.feature_fusion.attention(
                global_query, stacked_features, stacked_features
            )

            attention_weights['feature_fusion'] = fusion_weights.detach()
            attention_weights['hop_importance'] = self.get_hop_importance()
            attention_weights['ensemble_weights'] = self.get_ensemble_weights()

        # Restore training mode
        if was_training:
            self.train()

        return attention_weights

    def update_progressive_stage(self, epoch: int, total_epochs: int):
        """Update progressive training stage."""
        if self.use_progressive_training:
            progress = epoch / total_epochs
            new_stage = min(self.num_hops - 1, int(progress * self.num_hops))
            self.progressive_trainer.current_stage = new_stage


class SeHGNNAdapter(nn.Module):
    """Adapter to make SeHGNN compatible with standard interface."""

    def __init__(
        self,
        in_feats: int,
        hidden: int,
        out_feats: int,
        dropout: float,
        input_dropout: float,
        num_layers: int,
        num_etypes: int,
        num_heads: int,
    ):
        super().__init__()

        self.sehgnn = SeHGNN(
            in_feats=in_feats,
            hidden=hidden,
            out_feats=out_feats,
            num_hops=num_layers,
            dropout=dropout,
            num_ensemble_heads=num_heads,
        )

        self.linear = nn.Linear(in_feats, in_feats)
        self.label_linear = nn.Linear(out_feats, in_feats)
        self.input_dropout = nn.Dropout(input_dropout)
        self.num_layers = num_layers
        self.num_etypes = num_etypes

        # Multi-hop feature extractor
        self.hop_extractor = self._create_hop_extractor(hidden, num_layers, num_etypes)

    def _create_hop_extractor(self, hidden: int, num_layers: int, num_etypes: int):
        """Create multi-hop feature extractor."""
        return nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden, hidden),
                nn.ReLU(),
                nn.Dropout(0.1)
            ) for _ in range(num_layers)
        ])

    def forward(self, graph: dgl.DGLGraph, feat: torch.Tensor, label_feat: torch.Tensor) -> torch.Tensor:
        """Standard interface forward method."""
        # Combine node features and label features
        feat = self.linear(self.input_dropout(feat))
        label_feat = self.label_linear(self.input_dropout(label_feat))
        combined_feat = feat + label_feat

        # Extract multi-hop features
        hop_features = self._extract_hop_features(graph, combined_feat)

        # Call SeHGNN
        return self.sehgnn(hop_features)

    def _extract_hop_features(self, graph: dgl.DGLGraph, feat: torch.Tensor) -> List[torch.Tensor]:
        """Extract multi-hop features from the graph."""
        hop_features = [feat]  # 0-hop features
        current_feat = feat

        with graph.local_scope():
            for hop in range(1, self.num_layers):
                # Aggregate features from different edge types
                aggregated_feats = []

                for etype in range(self.num_etypes):
                    # Get edges of this type
                    edge_mask = graph.edata.get("_TYPE", torch.zeros(graph.num_edges(), dtype=torch.long)) == etype

                    if edge_mask.sum() > 0:
                        # Create subgraph for this edge type
                        subgraph = graph.edge_subgraph(edge_mask, preserve_nodes=True)
                        subgraph.ndata["feat"] = current_feat

                        # Message passing
                        subgraph.update_all(
                            dgl.function.copy_u("feat", "msg"),
                            dgl.function.mean("msg", "agg_feat")
                        )

                        agg_feat = subgraph.ndata.get("agg_feat", torch.zeros_like(current_feat))
                        aggregated_feats.append(agg_feat)
                    else:
                        aggregated_feats.append(torch.zeros_like(current_feat))

                # Combine features from different edge types
                if aggregated_feats:
                    combined_agg = torch.stack(aggregated_feats).mean(dim=0)
                else:
                    combined_agg = current_feat

                # Apply hop-specific transformation
                if hop - 1 < len(self.hop_extractor):
                    current_feat = self.hop_extractor[hop - 1](combined_agg)
                else:
                    current_feat = combined_agg

                hop_features.append(current_feat)

        return hop_features[:self.num_layers]
