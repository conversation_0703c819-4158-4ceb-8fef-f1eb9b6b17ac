import torch
from torch import nn
import torch.nn.functional as F
from .common import FeedForwardNet


class SeHGNN(nn.Module):
    """Self-ensemble Heterogeneous GNN (简化实现).

    SeHGNN 通过对 *R* 阶邻域特征进行并行处理并在推理阶段自蒸馏集成得到
    更鲁棒的结果。本实现关注“多阶特征 + 逐层自蒸馏”两个核心思想并采用
    mini-batch 方式计算损失：
      1. R-hop 特征通过 `feats` 列表传入；
      2. 每一阶特征先经过一个隐藏映射 (共享参数)；
      3. 训练过程中，模型输出 logits 同时作为软标签与硬标签进行自蒸馏；
         这里为了简化仅返回 logits，蒸馏逻辑应在损失函数中完成。
    """

    def __init__(self, in_feats: int, hidden: int, out_feats: int, num_hops: int, dropout: float = 0.5):
        super().__init__()
        self.num_hops = num_hops
        self.pre = FeedForwardNet(in_feats, hidden, hidden, 2, dropout)
        self.post = FeedForwardNet(hidden, hidden, out_feats, 2, dropout)
        self.dropout = nn.Dropout(dropout)

    def forward(self, feats):
        assert len(feats) == self.num_hops, "Expect num_hops hop features"
        hop_embeds = [self.pre(self.dropout(f)) for f in feats]
        h = sum(hop_embeds) / len(hop_embeds)
        return self.post(self.dropout(h)) 