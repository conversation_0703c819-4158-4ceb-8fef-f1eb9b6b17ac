import torch
from torch import nn
import torch.nn.functional as F
import dgl
import dgl.function as fn
from .common import FeedForwardNet, FeatAttention, WeightedAggregator


class RGCN_layer(nn.Module):
    def __init__(self, out_feats, num_heads, num_etypes, attn_drop: float = 0.2):
        super().__init__()
        self._num_heads = num_heads
        self._out_feats = out_feats
        self._num_etypes = num_etypes

    def forward(self, graph: dgl.DGLGraph, feat: torch.Tensor):
        with graph.local_scope():
            feat_skip = [feat]
            for i in range(self._num_etypes):
                mask = graph.edata["_TYPE"] == i
                subgraph = graph.edge_subgraph(mask, preserve_nodes=True)
                subgraph.ndata["feat"] = feat
                subgraph.update_all(fn.copy_u("feat", "msg"), fn.mean("msg", "feat_neighbor"))
                feat_skip.append(subgraph.ndata.pop("feat_neighbor"))
            feat = sum(feat_skip) / len(feat_skip)
            return F.relu(feat)


class RGCN(nn.Module):
    """Relation-aware Graph Convolutional Network"""

    def __init__(
        self,
        in_feats: int,
        hidden: int,
        out_feats: int,
        dropout: float,
        input_dropout: float,
        num_layers: int,
        num_etypes: int,
        num_heads: int,
    ):
        super().__init__()
        self.num_layers = num_layers
        self.out_feats = out_feats
        self.num_heads = num_heads
        self.hidden = hidden

        self.input_dropout = nn.Dropout(input_dropout)
        self.dropout = nn.Dropout(dropout)

        self.layers = nn.ModuleList()
        for _ in range(num_layers):
            self.layers.append(RGCN_layer(hidden // num_heads, num_heads, num_etypes))

        self.mlp = nn.Sequential(
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, out_feats),
        )

        self.linear = nn.Linear(in_feats, hidden)
        self.label_linear = nn.Linear(out_feats, hidden)
        self.norms = nn.ModuleList([nn.BatchNorm1d(hidden) for _ in range(num_layers)])
        self.inception_ffs = nn.ModuleList([
            FeedForwardNet(hidden, hidden, hidden, 1, dropout) for _ in range(num_layers)
        ])

    def forward(self, graph: dgl.DGLGraph, feat: torch.Tensor, label_feat: torch.Tensor):
        feat = self.linear(self.input_dropout(feat))
        label_feat = self.label_linear(self.input_dropout(label_feat))
        feat = feat + label_feat

        feat_hop = [feat]
        for layer in self.layers:
            feat = layer(graph, feat)
            feat_hop.append(feat)
        feat_hop = [self.input_dropout(f) for f in feat_hop]

        norm_feats = [bn(f) for f, bn in zip(feat_hop, self.norms)]

        tmp = [ff(f).view(-1, self.num_heads, self.hidden // self.num_heads) for f, ff in zip(norm_feats, self.inception_ffs)]
        feat = self.dropout((sum(tmp) / len(tmp)).view(-1, self.hidden))
        return self.mlp(feat) 