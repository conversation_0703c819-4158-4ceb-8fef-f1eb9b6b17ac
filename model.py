import math
import torch
from torch import nn
import torch.nn.functional as F
import dgl.function as fn
import dgl


class MLP(torch.nn.Module):
    def __init__(self, in_channels, hidden_channels, out_channels, num_layers,
                 dropout):
        super(MLP, self).__init__()

        self.lins = torch.nn.ModuleList()
        self.lins.append(torch.nn.Linear(in_channels, hidden_channels))
        for _ in range(num_layers - 2):
            self.lins.append(torch.nn.Linear(hidden_channels, hidden_channels))
        self.lins.append(torch.nn.Linear(hidden_channels, out_channels))

        self.dropout = dropout

    def reset_parameters(self):
        for lin in self.lins:
            lin.reset_parameters()

    def forward(self, x):
        for i, lin in enumerate(self.lins[:-1]):
            x = lin(x)
            x = F.relu(x)
            x = F.dropout(x, p=self.dropout, training=self.training)
        x = self.lins[-1](x)
        return torch.log_softmax(x, dim=-1)


class FeedForwardNet(nn.Module):
    def __init__(self, in_feats, hidden, out_feats, n_layers, dropout):
        super(FeedForwardNet, self).__init__()
        self.layers = nn.ModuleList()
        self.n_layers = n_layers
        if n_layers == 1:
            self.layers.append(nn.Linear(in_feats, out_feats))
        else:
            self.layers.append(nn.Linear(in_feats, hidden))
            for i in range(n_layers - 2):
                self.layers.append(nn.Linear(hidden, hidden))
            self.layers.append(nn.Linear(hidden, out_feats))
        if self.n_layers > 1:
            self.prelu = nn.PReLU()
            self.dropout = nn.Dropout(dropout)
        self.reset_parameters()

    def reset_parameters(self):
        gain = nn.init.calculate_gain("relu")
        for layer in self.layers:
            nn.init.xavier_uniform_(layer.weight, gain=gain)
            nn.init.zeros_(layer.bias)

    def forward(self, x):
        for layer_id, layer in enumerate(self.layers):
            x = layer(x)
            if layer_id < self.n_layers - 1:
                x = self.dropout(self.prelu(x))
        return x


class SIGN(nn.Module):
    # def __init__(self, in_feats, hidden, out_feats, num_hops, n_layers,
    #              dropout, input_drop, num_rel, orth_basis):
    def __init__(self, in_feats, hidden, out_feats, num_hops, n_layers,
                 dropout, input_drop):
        super(SIGN, self).__init__()
        self.dropout = nn.Dropout(dropout)
        self.prelu = nn.PReLU()
        self.inception_ffs = nn.ModuleList()
        self.input_drop = nn.Dropout(input_drop)
        for hop in range(num_hops):
            self.inception_ffs.append(
                FeedForwardNet(in_feats, hidden, hidden, n_layers, dropout))
        self.project = FeedForwardNet(num_hops * hidden, hidden, out_feats,
                                      n_layers, dropout)
        # self.rel_aggre_layer = RelationAggregationNet(in_feats, hidden, dropout, num_rel, num_hops, orth_basis)
        self.norm_ffs = nn.ModuleList()
        for hop in range(num_hops):
            self.norm_ffs.append(
                nn.BatchNorm1d(in_feats))

    def forward(self, feats):
        feats = [self.input_drop(feat) for feat in feats]

        norm_feats = []
        for feat, ff in zip(feats, self.norm_ffs):
            norm_feats.append(ff(feat))
        # norm_feats = self.rel_aggre_layer(norm_feats)
        hidden = []
        for feat, ff in zip(norm_feats, self.inception_ffs):
            hidden.append(ff(feat))
        out = self.project(self.dropout(self.prelu(torch.cat(hidden, dim=-1))))
        return out

    def reset_parameters(self):
        for ff in self.inception_ffs:
            ff.reset_parameters()
        self.project.reset_parameters()


class FeatAttention(nn.Module):
    def __init__(self, feat_dim, num_heads, attn_drop=0.0, negative_slope=0.2):
        super(FeatAttention, self).__init__()
        self.hop_attn_l = nn.Parameter(torch.FloatTensor(size=(1, num_heads, feat_dim)))
        self.hop_attn_r = nn.Parameter(torch.FloatTensor(size=(1, num_heads, feat_dim)))
        self.attn_dropout = nn.Dropout(attn_drop)
        self.leaky_relu = nn.LeakyReLU(negative_slope)
        self.reset_parameters()
    
    def reset_parameters(self):
        gain = nn.init.calculate_gain("relu")
        nn.init.xavier_normal_(self.hop_attn_l, gain=gain)
        nn.init.xavier_normal_(self.hop_attn_r, gain=gain)

    def forward(self, feats):
        astack_l = [(feat * self.hop_attn_l).sum(dim=-1).unsqueeze(-1) for feat in feats]
        a_r = (feats[0] * self.hop_attn_r).sum(dim=-1).unsqueeze(-1)
        astack = torch.cat([(a_l + a_r).unsqueeze(-1) for a_l in astack_l], dim=-1)
        a = self.leaky_relu(astack)
        a = F.softmax(a, dim=-1)
        a = self.attn_dropout(a)
        out = 0
        for i in range(a.shape[-1]):
            out += feats[i] * a[:, :, :, i]
        return out

class RGCN_layer(nn.Module):
    def __init__(self,
                 out_feats,
                 num_heads,
                 num_etypes,
                 attn_drop=0.2):
        super(RGCN_layer, self).__init__()
        self._num_heads = num_heads
        self._out_feats = out_feats
        self._num_etypes = num_etypes
        self.reset_parameters()
        
    def reset_parameters(self):
        gain = nn.init.calculate_gain('relu')

    def forward(self, graph, feat):
        with graph.local_scope():
            feat_skip = [feat]
            for i in range(self._num_etypes):
                mask = graph.edata["_TYPE"] == i
                subgraph = graph.edge_subgraph(mask, preserve_nodes=True)
                subgraph.ndata["feat"] = feat
                subgraph.update_all(fn.copy_u("feat", "msg"),
                                    fn.mean("msg", "feat_neighbor"))
                feat_skip.append(subgraph.ndata.pop("feat_neighbor"))
            feat = (sum(feat_skip) / len(feat_skip))
            feat = F.relu(feat)
            return feat


class RGCN(nn.Module):
    def __init__(self, in_feats, hidden, out_feats, dropout, input_dropout, num_layers, num_etypes, num_heads):
        super(RGCN, self).__init__()
        self.num_layers = num_layers
        self.out_feats = out_feats
        self.num_heads = num_heads
        self.hidden = hidden
        self.input_dropout = nn.Dropout(input_dropout)
        self.dropout = nn.Dropout(dropout)
        self.rgat = nn.ModuleList([RGCN_layer(hidden // num_heads, num_heads, num_etypes)])
        for i in range(num_layers - 1):
            self.rgat.append(RGCN_layer(hidden // num_heads, num_heads, num_etypes))

        
        self.mlp = nn.Sequential(
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, out_feats)
        )
        self.linear = nn.Linear(in_feats, hidden)
        self.label_linear = nn.Linear(out_feats, hidden)
        self.norms = nn.ModuleList([nn.BatchNorm1d(hidden) for i in range(num_layers)])
        self.inception_ffs = nn.ModuleList()
        for hop in range(num_layers):
            self.inception_ffs.append(
                FeedForwardNet(hidden, hidden, hidden, 1, dropout))

    
    def forward(self, graph, feat, label_feat):
        feat = self.linear(self.input_dropout(feat))
        label_feat = self.label_linear(self.input_dropout(label_feat))
        feat += label_feat
        feat_hop = [feat]
        # feat_hop = []
        for i in range(self.num_layers):
            feat = self.rgat[i](graph, feat)
            feat_hop.append(feat)
        feat_hop = [self.input_dropout(feat_h) for feat_h in feat_hop]
        
        norm_feats = []
        for _feat, ff in zip(feat_hop, self.norms):
            norm_feats.append(ff(_feat))

        tmp = []
        for _feat, _ff in zip(norm_feats, self.inception_ffs):
            tmp.append(_ff(_feat).view(-1, self.num_heads, self.hidden // self.num_heads))
        feat = self.dropout((sum(tmp) / len(tmp)).view(-1, self.hidden))
        return self.mlp(feat)


class RGAT_layer(nn.Module):
    def __init__(self,
                 out_feats,
                 num_heads,
                 num_etypes,
                 attn_drop=0.2):
        super(RGAT_layer, self).__init__()
        self._num_heads = num_heads
        self._out_feats = out_feats
        self._num_etypes = num_etypes
        self.attn_layer = FeatAttention(out_feats, num_heads, attn_drop, negative_slope=0.2)
        # self.linears = nn.ModuleList([nn.Linear(num_heads * out_feats, num_heads * out_feats) for i in range(num_etypes)])
        self.reset_parameters()
        
    def reset_parameters(self):
        gain = nn.init.calculate_gain('relu')
        # for i in range(self._num_etypes):
        #     nn.init.xavier_uniform_(self.linears[i].weight, gain=gain)
        #     if isinstance(self.linears[i].bias, nn.Parameter):
        #         nn.init.zeros_(self.linears[i].bias)

    def forward(self, graph, feat):
        with graph.local_scope():
            feat_skip = [feat.view(-1, self._num_heads, self._out_feats)]
            for i in range(self._num_etypes):
                mask = graph.edata["_TYPE"] == i
                subgraph = graph.edge_subgraph(mask, preserve_nodes=True)
                # subgraph.ndata["feat"] = feat
                subgraph.update_all(fn.copy_u("feat", "msg"),
                                    fn.mean("msg", "feat_neighbor"))
                feat_skip.append(subgraph.ndata.pop("feat_neighbor").view(-1, self._num_heads, self._out_feats))
            feat = self.attn_layer(feat_skip).view(-1, self._num_heads * self._out_feats)
            # feat = (sum(feat_skip) / len(feat_skip)).view(-1, self._num_heads * self._out_feats)
            feat = F.relu(feat)
            return feat


class RGAT(nn.Module):
    def __init__(self, in_feats, hidden, out_feats, dropout, input_dropout, num_layers, num_etypes, num_heads):
        super(RGAT, self).__init__()
        self.num_layers = num_layers
        self.out_feats = out_feats
        self.num_heads = num_heads
        self.hidden = hidden
        self.input_dropout = nn.Dropout(input_dropout)
        self.dropout = nn.Dropout(dropout)
        self.rgat = nn.ModuleList()
        for i in range(num_layers):
            self.rgat.append(RGAT_layer(hidden // num_heads, num_heads, num_etypes))

        
        self.mlp = nn.Sequential(
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, out_feats)
        )
        # self.attn_layer = FeatAttention(hidden // num_heads, num_heads)
        self.linear = nn.Linear(in_feats, hidden)

        self.label_linear = nn.Linear(out_feats, hidden)

        self.norms = nn.ModuleList([nn.BatchNorm1d(hidden) for i in range(num_layers + 1)])
        self.inception_ffs = nn.ModuleList()
        for hop in range(num_layers + 1):
            self.inception_ffs.append(
                FeedForwardNet(hidden, hidden, hidden, 1, dropout))
        # self.linear_res = nn.Linear(hidden, hidden)
        # self.label_linear_res = nn.Linear(hidden, hidden)

    
    def forward(self, graph, feat, label_feat):
        feat = self.linear(self.input_dropout(feat))
        label_feat = self.label_linear(self.input_dropout(label_feat))
        feat += label_feat
        feat_hop = [feat]
        for i in range(self.num_layers):
            graph.ndata["feat"] = feat
            feat = self.rgat[i](graph, feat)
            feat_hop.append(feat)
        feat_hop = [self.input_dropout(feat_h) for feat_h in feat_hop]
        
        norm_feats = []
        for _feat, ff in zip(feat_hop, self.norms):
            norm_feats.append(ff(_feat))
        # norm_feats = []
        # for _feat, ff in zip(feat_hop, self.norms):
        #     norm_feats.append(ff(_feat).view(-1, self.num_heads, self.hidden // self.num_heads))


        tmp = []
        for _feat, _ff in zip(norm_feats, self.inception_ffs):
            tmp.append(_ff(_feat).view(-1, self.num_heads, self.hidden // self.num_heads))
        # feat_combine = self.dropout(self.attn_layer(tmp).view(-1, self.hidden))
        # feat_combine = self.dropout(self.attn_layer(norm_feats).view(-1, self.hidden))
        feat = self.dropout((sum(tmp) / len(tmp)).view(-1, self.hidden))

        return self.mlp(feat)


class AttentionLayer(nn.Module):
    def __init__(self, feat_dim):
        super(AttentionLayer, self).__init__()
        self.hop_attn_l = nn.Parameter(torch.FloatTensor(size=(1, feat_dim)), requires_grad=True)
        self.hop_attn_r = nn.Parameter(torch.FloatTensor(size=(1, feat_dim)), requires_grad=True)
        self.reset_parameters()
    
    def reset_parameters(self):
        gain = nn.init.calculate_gain("relu")
        nn.init.xavier_normal_(self.hop_attn_l, gain=gain)
        nn.init.xavier_normal_(self.hop_attn_r, gain=gain)

    def forward(self, feats):
        feats_mean = [F.normalize(feat.mean(0, keepdim=True)) for feat in feats]
        feat_mean = F.normalize(sum(feats_mean) / len(feats))
        astack_l = [(feat * self.hop_attn_l).sum(dim=-1) for feat in feats_mean]
        a_r = (feat_mean * self.hop_attn_r).sum(dim=-1)
        astack = torch.tensor([(a_l + a_r) for a_l in astack_l])
        a = F.softmax(astack, dim=-1)
        # print(a)
        out = 0
        for i in range(a.shape[-1]):
            out += feats[i] * a[i]
        return out

class RGSN_layer(nn.Module):
    def __init__(self,
                 out_feats,
                 num_heads,
                 num_etypes,
                 attn_drop=0.2):
        super(RGSN_layer, self).__init__()
        self._out_feats = out_feats
        self._num_etypes = num_etypes
        self._num_heads = num_heads
        self.attn_dropout = nn.Dropout(attn_drop)
        self.leaky_relu = nn.LeakyReLU(0.2)
        self.attn_layer = FeatAttention(out_feats, num_heads, attn_drop, negative_slope=0.2)
        self.intra_attn_l = nn.Parameter(torch.Tensor(num_etypes, 1, num_heads, out_feats), requires_grad=True)
        self.intra_attn_r = nn.Parameter(torch.Tensor(num_etypes, 1, num_heads, out_feats), requires_grad=True)
        self.reset_parameters()
        
    def reset_parameters(self):
        gain = nn.init.calculate_gain('relu')
        nn.init.xavier_normal_(self.intra_attn_l, gain=gain)
        nn.init.xavier_normal_(self.intra_attn_r, gain=gain)

    def forward(self, graph, feat):
        with graph.local_scope():
            feat = F.normalize(feat)
            feat_skip = [feat.view(-1, self._num_heads, self._out_feats)]
            graph.ndata["feat"] = feat.view(-1, self._num_heads, self._out_feats)
            for i in range(self._num_etypes):
                mask = graph.edata["_TYPE"] == i
                subgraph = graph.edge_subgraph(mask, preserve_nodes=True)
                graph = dgl.add_self_loop(graph)
                subgraph.update_all(fn.copy_u("feat", "msg"),
                                    fn.mean("msg", "feat_neighbor"))
                graph = dgl.remove_self_loop(graph)
                # subgraph.ndata["feat_neighbor"] += graph.ndata["feat"] = feat
                # mean_feat = F.normalize(subgraph.ndata.pop("feat_neighbor"))
                # mean_feat = subgraph.ndata.pop("feat_neighbor")

                subgraph.ndata["left"] = (subgraph.ndata["feat"] * self.intra_attn_l[i]).sum(-1).unsqueeze(-1)
                subgraph.ndata["right"] = (subgraph.ndata["feat_neighbor"] * self.intra_attn_r[i]).sum(-1).unsqueeze(-1)
                # subgraph.apply_edges(lambda edges: {'a' : (edges.src["feat"] * self.intra_attn_l[i]).sum(-1) + (mean_feat[edges.src["_ID"]] * self.intra_attn_r[i]).sum(-1)})
                subgraph.apply_edges(fn.u_add_v("left", "right", "a"))
                subgraph.edata["a"] = self.leaky_relu(subgraph.edata["a"].unsqueeze(-1))
                subgraph.edata["a"] = dgl.nn.functional.edge_softmax(subgraph, subgraph.edata["a"], norm_by='dst')
                subgraph.edata["a"] = self.attn_dropout(subgraph.edata["a"]).view(-1, self._num_heads, 1)
                subgraph.update_all(fn.u_mul_e("feat", "a", "msg"),
                             fn.sum("msg", "feat_neighbor"))
                feat_skip.append(subgraph.ndata.pop("feat_neighbor"))
            feat = self.attn_layer(feat_skip).view(-1, self._num_heads * self._out_feats)
            feat = F.relu(feat)
            return feat


class RGSN(nn.Module):
    def __init__(self, in_feats, hidden, out_feats, dropout, input_dropout, num_layers, num_etypes, num_heads):
        super(RGSN, self).__init__()
        self.num_layers = num_layers
        self.out_feats = out_feats
        self.hidden = hidden
        self.input_dropout = nn.Dropout(input_dropout)
        self.dropout = nn.Dropout(dropout)
        self.rgat = nn.ModuleList()
        for i in range(num_layers):
            self.rgat.append(RGSN_layer(hidden // num_heads, num_heads, num_etypes))

        self.mlp = nn.Sequential(
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, out_feats)
        )
        self.linear = nn.Linear(in_feats, hidden)
        self.label_linear = nn.Linear(out_feats, hidden)
        self.norms = nn.ModuleList([nn.BatchNorm1d(hidden) for i in range(num_layers + 1)])
        self.inception_ffs = nn.ModuleList()
        for hop in range(num_layers + 1):
            self.inception_ffs.append(
                FeedForwardNet(hidden, hidden, hidden, 1, dropout))
    
    def forward(self, graph, feat, label_feat):
        feat = self.linear(self.input_dropout(feat))
        label_feat = self.label_linear(self.input_dropout(label_feat))
        feat += label_feat
        feat_hop = [feat]
        for i in range(self.num_layers):
            feat = self.rgat[i](graph, feat)
            feat_hop.append(feat)
        feat_hop = [self.input_dropout(feat_h) for feat_h in feat_hop]
        
        norm_feats = []
        for _feat, ff in zip(feat_hop, self.norms):
            norm_feats.append(ff(_feat))

        tmp = []
        for _feat, _ff in zip(norm_feats, self.inception_ffs):
            tmp.append(_ff(_feat))
        feat = self.dropout((sum(tmp) / len(tmp)).view(-1, self.hidden))
        return self.mlp(feat)


class WeightedAggregator(nn.Module):
    def __init__(self, num_feats, in_feats, num_hops):
        super(WeightedAggregator, self).__init__()
        self.agg_feats = nn.ParameterList()
        for _ in range(num_hops):
            self.agg_feats.append(nn.Parameter(torch.Tensor(num_feats, in_feats)))
            nn.init.xavier_uniform_(self.agg_feats[-1])

    def forward(self, feats):
        new_feats = []
        for feat, weight in zip(feats, self.agg_feats):
            new_feats.append((feat * weight.unsqueeze(0)).sum(dim=1).squeeze())
        return new_feats
