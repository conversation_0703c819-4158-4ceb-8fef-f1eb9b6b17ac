# SubInfer
the source code and supplementary materials of paper "An Efficient Subgraph-inferring Framework for Large-scale Heterogeneous Graphs"

## Dataset
The download links for each dataset are as follows:

Ogbn-mag: https://ogb.stanford.edu/docs/nodeprop/#ogbn-mag

DBLP, Yelp, Pubmed: https://drive.google.com/drive/folders/1Pkbl2wkwAXVRYrUWKpa1C4YQdjl_oIu2

Freebase: https://www.biendata.xyz/hgb/#/datasets

## Requirements
torch 1.10.1

dgl 0.9.0

ogb >= 1.3.5

scipy >= 1.7.3

numpy >= 1.21.2

pytorch_geometric >= 2.0.4


### other dependencies

```
pip install -r requirements.txt
git clone https://github.com/Yangxc13/sparse_tools.git --depth=1
cd sparse_tools
python setup.py develop
```


## Pre-processing
To generate features for nodes lacking attributes using the complEx model, please visit the following link: https://github.com/ICT-GIMLab/SeHGNN/tree/master/ogbn


Then, you need to execute the following command to process the data format.

`python Data_preprocess.py`


## Running

If you aim to replicate the results of SubInfer, modify the **run.sh** file, and execute it to obtain the final experimental results.
