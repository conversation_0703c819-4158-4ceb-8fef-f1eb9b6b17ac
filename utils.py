import random
import dgl
import torch.utils.data
import time
import scipy.sparse as sp
from tqdm import tqdm
from ogb.nodeproppred import Evaluator


class ClusterData(torch.utils.data.Dataset):
    r"""Clusters/partitions a graph data object into multiple subgraphs, as
    motivated by the `"Cluster-GCN: An Efficient Algorithm for Training Deep
    and Large Graph Convolutional Networks"
    <https://arxiv.org/abs/1905.07953>`_ paper.

    Args:
        data (torch_geometric.data.Data): The graph data object.
        num_parts (int): The number of partitions.
        recursive (bool, optional): If set to :obj:`True`, will use multilevel
            recursive bisection instead of multilevel k-way partitioning.
            (default: :obj:`False`)
        save_dir (string, optional): If set, will save the partitioned data to
            the :obj:`save_dir` directory for faster re-use.
            (default: :obj:`None`)
        log (bool, optional): If set to :obj:`False`, will not log any
            progress. (default: :obj:`True`)
    """
    def __init__(self, clusters, influential_nodes, graph, add_nodes=False):
        self.clusters = clusters
        self.graph = graph
        self.add_nodes = add_nodes
        self.influential_nodes = influential_nodes
        # self.data = Data(num_nodes=self.graph.num_nodes())

    def __len__(self):
        return len(self.clusters)

    def __getitem__(self, idx):
        mask = torch.tensor([True for i in range(len(self.clusters[idx]))] + 
                            [False for j in range(len(self.influential_nodes[idx]))])
        subgraph = dgl.node_subgraph(self.graph, self.clusters[idx] + self.influential_nodes[idx])
        subgraph.ndata["mask"] = mask
        return subgraph


class ClusterHeterData(torch.utils.data.Dataset):
    r"""Clusters/partitions a graph data object into multiple subgraphs, as
    motivated by the `"Cluster-GCN: An Efficient Algorithm for Training Deep
    and Large Graph Convolutional Networks"
    <https://arxiv.org/abs/1905.07953>`_ paper.

    Args:
        data (torch_geometric.data.Data): The graph data object.
        num_parts (int): The number of partitions.
        recursive (bool, optional): If set to :obj:`True`, will use multilevel
            recursive bisection instead of multilevel k-way partitioning.
            (default: :obj:`False`)
        save_dir (string, optional): If set, will save the partitioned data to
            the :obj:`save_dir` directory for faster re-use.
            (default: :obj:`None`)
        log (bool, optional): If set to :obj:`False`, will not log any
            progress. (default: :obj:`True`)
    """
    def __init__(self, clusters, influential_nodes, graph, offset, node_type):
        self.clusters = clusters
        self.influential_nodes = influential_nodes
        self.graph = graph
        self.offset = offset
        self.node_type = node_type

    def __len__(self):
        return len(self.clusters)

    def __getitem__(self, idx):
        cluster = self.clusters[idx] + self.influential_nodes[idx]
        cluster = torch.tensor(cluster)
        nodes = {"author": [], "field": [], "institution": [], "paper": []}
        node_type = self.node_type[cluster]
        nodes["author"] = cluster[node_type == 0] - self.offset["author"]
        nodes["field"] = cluster[node_type == 1] - self.offset["field"]
        nodes["institution"] = cluster[node_type == 2] - self.offset["institution"]
        nodes["paper"] = cluster[node_type == 3] - self.offset["paper"]
        num_target_nodes = torch.sum(self.node_type[self.clusters[idx]] == 3)

        subgraph = dgl.node_subgraph(self.graph, nodes)
        mask = torch.tensor([False for i in range(subgraph.num_nodes("paper"))])
        mask[:num_target_nodes] = True
        subgraph.nodes["paper"].data["mask"] = mask
        # subgraph.nodes["author"].data["NID"] = subgraph.nodes["author"].data[dgl.NID]
        # subgraph.nodes["field"].data["NID"] = subgraph.nodes["field"].data[dgl.NID]
        # subgraph.nodes["institution"].data["NID"] = subgraph.nodes["institution"].data[dgl.NID]
        # subgraph.nodes["paper"].data["NID"] = subgraph.nodes["paper"].data[dgl.NID]
        return subgraph


def cluster_by_metis(coo_matirx, num_parts):
    g = dgl.graph((coo_matirx.row, coo_matirx.col))
    subgraphs = dgl.metis_partition(g, num_parts).values()
    clusters = []
    for subgraph in subgraphs:
        clusters.append(subgraph.ndata[dgl.NID].tolist())
    return clusters


def get_ogb_evaluator(dataset):
    """
    Get evaluator from Open Graph Benchmark based on dataset
    """
    evaluator = Evaluator(name=dataset)
    return lambda preds, labels: evaluator.eval({
        "y_true": labels.view(-1, 1),
        "y_pred": preds.view(-1, 1),
    })["acc"]


def get_n_params(model):
    pp = 0
    for p in list(model.parameters()):
        nn = 1
        for s in list(p.size()):
            nn = nn * s
        pp += nn
    return pp

def cluster(node2node, num_parts, nodes, adj, idx):
    # The effect of this method is comparable to that of allocating nodes via the greedy algorithm and uses less time.
    t_1 = time.time()
    sub_mat = [[] for i in range(len(nodes) - 1)]
    for i in range(len(nodes) - 1):
        for j in range(len(nodes) - 1):
            sub_mat[i].append(adj[nodes[i]: nodes[i + 1], nodes[j]: nodes[j + 1]])
    sub_mat[idx][idx] = node2node
    new_adj = sp.bmat(sub_mat, dtype=adj.dtype)
    clusters = cluster_by_metis(new_adj.tocoo(), num_parts=num_parts)
    print("clustering costs {:.4f}s".format(time.time() - t_1))
    return clusters


def global_clusters(adj, clusters, limit, node_degree):
    trans_matrix = sp.lil_matrix((adj.shape[0], len(clusters)))
    for i in tqdm(range(len(clusters))):
        for j in clusters[i]:
            trans_matrix[j, i] = 1
    trans_matrix = trans_matrix.tocsr()
    local2global = adj.dot(trans_matrix).toarray()
    mask = torch.ones((len(clusters), adj.shape[0]))
    for i in range(mask.shape[0]):
        mask[i][clusters[i]] = 0
    local2global = torch.mul(torch.from_numpy(local2global), mask.T)
    values_nodes, indices_nodes = local2global.topk(limit, dim=0, largest=True, sorted=True)
    return indices_nodes.T.tolist()


def accuracy(preds, labels):
    result = torch.tensor([preds[i] == labels[i] for i in range(preds.shape[0])])
    return (torch.sum(result) / preds.shape[0]).item()


def distributed_sum(prob):
    output_tensors = [prob.clone() for _ in range(torch.distributed.get_world_size())]
    torch.distributed.all_gather(output_tensors, prob)
    result= sum(output_tensors)
    # truncate the dummy elements added by SequentialDistributedSampler
    return result