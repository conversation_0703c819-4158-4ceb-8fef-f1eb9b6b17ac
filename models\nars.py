import torch
from torch import nn
import torch.nn.functional as F
import dgl
import dgl.function as fn
from .common import FeedForwardNet, WeightedAggregator
import numpy as np
from typing import List, Dict, Tuple, Optional


class RelationSubsetAggregator(nn.Module):
    """Aggregates features from relation-specific subgraphs."""

    def __init__(self, in_feats: int, out_feats: int, num_relations: int,
                 aggregation_type: str = "mean"):
        super().__init__()
        self.in_feats = in_feats
        self.out_feats = out_feats
        self.num_relations = num_relations
        self.aggregation_type = aggregation_type

        # Relation-specific linear transformations
        self.rel_linears = nn.ModuleList([
            nn.Linear(in_feats, out_feats) for _ in range(num_relations)
        ])

        # Attention weights for relation importance
        self.rel_attention = nn.Parameter(torch.FloatTensor(num_relations, 1))

        # Normalization
        self.layer_norm = nn.LayerNorm(out_feats)

        self.reset_parameters()

    def reset_parameters(self):
        for linear in self.rel_linears:
            nn.init.xavier_uniform_(linear.weight)
            nn.init.zeros_(linear.bias)
        nn.init.xavier_uniform_(self.rel_attention)

    def forward(self, relation_feats: List[torch.Tensor]) -> torch.Tensor:
        """
        Args:
            relation_feats: List of features from different relation subsets
                          Each tensor has shape [num_nodes, in_feats]
        Returns:
            Aggregated features [num_nodes, out_feats]
        """
        assert len(relation_feats) == self.num_relations

        # Transform features for each relation
        transformed_feats = []
        for i, feat in enumerate(relation_feats):
            transformed = self.rel_linears[i](feat)
            transformed_feats.append(transformed)

        # Stack and apply attention
        stacked_feats = torch.stack(transformed_feats, dim=0)  # [num_relations, num_nodes, out_feats]

        # Compute attention weights
        attn_weights = F.softmax(self.rel_attention, dim=0)  # [num_relations, 1]

        # Weighted aggregation
        aggregated = torch.sum(stacked_feats * attn_weights.unsqueeze(-1), dim=0)

        return self.layer_norm(aggregated)


class MultiHopFeatureExtractor(nn.Module):
    """Extracts multi-hop features from heterogeneous graphs."""

    def __init__(self, max_hops: int, num_relations: int):
        super().__init__()
        self.max_hops = max_hops
        self.num_relations = num_relations

    def extract_hop_features(self, graph: dgl.DGLGraph, features: torch.Tensor,
                           relation_types: torch.Tensor) -> List[List[torch.Tensor]]:
        """
        Extract multi-hop features for each relation type.

        Args:
            graph: DGL graph
            features: Node features [num_nodes, feat_dim]
            relation_types: Edge relation types [num_edges]

        Returns:
            List of lists: hop_features[hop][relation] = features
        """
        hop_features = []
        current_features = features

        for hop in range(self.max_hops):
            relation_features = []

            for rel_id in range(self.num_relations):
                # Create subgraph for this relation
                rel_mask = relation_types == rel_id
                if rel_mask.sum() > 0:
                    rel_subgraph = graph.edge_subgraph(rel_mask, preserve_nodes=True)

                    with rel_subgraph.local_scope():
                        rel_subgraph.ndata['h'] = current_features

                        # Aggregate neighbors
                        rel_subgraph.update_all(
                            fn.copy_u('h', 'm'),
                            fn.mean('m', 'h_agg')
                        )

                        # Get aggregated features, handle nodes with no neighbors
                        agg_features = rel_subgraph.ndata.get('h_agg',
                                                            torch.zeros_like(current_features))

                        # Fill missing values with original features
                        mask = (rel_subgraph.in_degrees() == 0)
                        agg_features[mask] = current_features[mask]

                        relation_features.append(agg_features)
                else:
                    # No edges of this relation type
                    relation_features.append(torch.zeros_like(current_features))

            hop_features.append(relation_features)

            # Update current features for next hop (average across relations)
            if relation_features:
                current_features = torch.stack(relation_features).mean(dim=0)

        return hop_features


class AdaptiveHopAggregator(nn.Module):
    """Adaptively aggregates features across different hops."""

    def __init__(self, feat_dim: int, num_hops: int, num_heads: int = 4):
        super().__init__()
        self.feat_dim = feat_dim
        self.num_hops = num_hops
        self.num_heads = num_heads

        # Multi-head attention for hop aggregation
        self.hop_attention = nn.MultiheadAttention(
            feat_dim, num_heads, batch_first=True
        )

        # Learnable hop embeddings
        self.hop_embeddings = nn.Embedding(num_hops, feat_dim)

        # Output projection
        self.output_proj = nn.Linear(feat_dim, feat_dim)

    def forward(self, hop_features: List[torch.Tensor]) -> torch.Tensor:
        """
        Args:
            hop_features: List of features from different hops
                         Each tensor has shape [num_nodes, feat_dim]
        Returns:
            Aggregated features [num_nodes, feat_dim]
        """
        num_nodes = hop_features[0].size(0)

        # Stack hop features
        stacked_features = torch.stack(hop_features, dim=1)  # [num_nodes, num_hops, feat_dim]

        # Add hop embeddings
        hop_ids = torch.arange(self.num_hops, device=stacked_features.device)
        hop_emb = self.hop_embeddings(hop_ids).unsqueeze(0)  # [1, num_hops, feat_dim]
        stacked_features = stacked_features + hop_emb

        # Apply self-attention across hops
        attended_features, _ = self.hop_attention(
            stacked_features, stacked_features, stacked_features
        )

        # Average across hops
        aggregated = attended_features.mean(dim=1)  # [num_nodes, feat_dim]

        return self.output_proj(aggregated)


class NARS(nn.Module):
    """Complete implementation of NARS (Neighborhood Aggregation over Relation Subsets).

    This implementation includes:
    1. Multi-hop neighborhood feature extraction for each relation type
    2. Relation-specific subgraph processing with learnable transformations
    3. Adaptive aggregation across different hops using attention mechanisms
    4. Learnable weights for combining features from different relation subsets
    5. Support for both online and offline feature computation
    6. Proper normalization and regularization techniques
    """

    def __init__(
        self,
        in_feats: int,
        hidden: int,
        out_feats: int,
        num_hops: int,
        num_relations: int,
        dropout: float = 0.5,
        use_online_computation: bool = True,
        num_attention_heads: int = 4,
        use_residual: bool = True,
        use_layer_norm: bool = True
    ):
        super().__init__()
        self.in_feats = in_feats
        self.hidden = hidden
        self.out_feats = out_feats
        self.num_hops = num_hops
        self.num_relations = num_relations
        self.use_online_computation = use_online_computation
        self.use_residual = use_residual
        self.use_layer_norm = use_layer_norm

        # Input feature projection
        self.input_proj = nn.Linear(in_feats, hidden)

        # Multi-hop feature extractor (for online computation)
        if use_online_computation:
            self.feature_extractor = MultiHopFeatureExtractor(num_hops, num_relations)

        # Relation subset aggregators for each hop
        self.hop_aggregators = nn.ModuleList([
            RelationSubsetAggregator(hidden, hidden, num_relations)
            for _ in range(num_hops)
        ])

        # Adaptive hop aggregator
        self.hop_aggregator = AdaptiveHopAggregator(hidden, num_hops, num_attention_heads)

        # Enhanced weighted aggregator with attention
        self.enhanced_aggregator = EnhancedWeightedAggregator(
            hidden, hidden, num_hops, num_relations
        )

        # Feature fusion layers
        self.fusion_layers = nn.ModuleList([
            FeedForwardNet(hidden, hidden, hidden, 2, dropout),
            FeedForwardNet(hidden, hidden, hidden, 2, dropout)
        ])

        # Output layers
        self.output_layers = nn.Sequential(
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, hidden // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden // 2, out_feats)
        )

        # Regularization
        self.dropout = nn.Dropout(dropout)
        self.input_dropout = nn.Dropout(dropout * 0.5)

        if use_layer_norm:
            self.layer_norms = nn.ModuleList([
                nn.LayerNorm(hidden) for _ in range(3)
            ])

        # Residual projection if needed
        if use_residual and in_feats != hidden:
            self.residual_proj = nn.Linear(in_feats, hidden)
        elif use_residual:
            self.residual_proj = nn.Identity()

        self.reset_parameters()

    def reset_parameters(self):
        """Initialize model parameters."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.xavier_uniform_(module.weight)

    def forward(self, graph_or_feats, features=None, relation_types=None):
        """
        Forward pass of NARS.

        Args:
            graph_or_feats: Either a DGL graph (online mode) or pre-computed features (offline mode)
            features: Node features (required for online mode)
            relation_types: Edge relation types (required for online mode)

        Returns:
            Output node features
        """
        if self.use_online_computation:
            return self._forward_online(graph_or_feats, features, relation_types)
        else:
            return self._forward_offline(graph_or_feats)

    def _forward_online(self, graph: dgl.DGLGraph, features: torch.Tensor,
                       relation_types: torch.Tensor) -> torch.Tensor:
        """Online computation mode - extract features from graph."""
        # Input projection with dropout
        h = self.input_dropout(features)
        h = F.relu(self.input_proj(h))

        # Store original features for residual connection
        if self.use_residual:
            residual = self.residual_proj(features)

        # Extract multi-hop features for each relation
        hop_relation_features = self.feature_extractor.extract_hop_features(
            graph, h, relation_types
        )

        # Aggregate features for each hop across relations
        hop_features = []
        for hop_idx, relation_feats in enumerate(hop_relation_features):
            aggregated = self.hop_aggregators[hop_idx](relation_feats)
            if self.use_layer_norm:
                aggregated = self.layer_norms[0](aggregated)
            hop_features.append(aggregated)

        # Aggregate across hops
        h_hop = self.hop_aggregator(hop_features)
        if self.use_layer_norm:
            h_hop = self.layer_norms[1](h_hop)

        # Enhanced aggregation with attention
        h_enhanced = self.enhanced_aggregator(hop_features, hop_relation_features)

        # Feature fusion
        h_fused = self.fusion_layers[0](h_hop) + self.fusion_layers[1](h_enhanced)
        if self.use_layer_norm:
            h_fused = self.layer_norms[2](h_fused)

        # Add residual connection
        if self.use_residual:
            h_fused = h_fused + residual

        # Output projection
        output = self.output_layers(self.dropout(h_fused))

        return output

    def _forward_offline(self, feats: List[torch.Tensor]) -> torch.Tensor:
        """Offline computation mode - use pre-computed features."""
        assert len(feats) == self.num_hops, f"Expected {self.num_hops} hop features, got {len(feats)}"

        # Project input features
        projected_feats = []
        for feat in feats:
            h = self.input_dropout(feat)
            h = F.relu(self.input_proj(h))
            projected_feats.append(h)

        # Store for residual connection
        if self.use_residual:
            residual = self.residual_proj(feats[0])  # Use 0-hop features

        # Use traditional weighted aggregation for offline mode
        agg_feats = self.enhanced_aggregator.simple_aggregate(projected_feats)

        # Apply fusion
        h_fused = self.fusion_layers[0](agg_feats)
        if self.use_layer_norm:
            h_fused = self.layer_norms[0](h_fused)

        # Add residual connection
        if self.use_residual:
            h_fused = h_fused + residual

        # Output projection
        output = self.output_layers(self.dropout(h_fused))

        return output

    def get_relation_importance(self) -> Dict[int, float]:
        """Get learned importance weights for each relation type."""
        importance_weights = {}

        for hop_idx, aggregator in enumerate(self.hop_aggregators):
            rel_weights = F.softmax(aggregator.rel_attention, dim=0).detach().cpu().numpy()
            for rel_idx, weight in enumerate(rel_weights):
                key = f"hop_{hop_idx}_rel_{rel_idx}"
                importance_weights[key] = float(weight)

        return importance_weights


class EnhancedWeightedAggregator(nn.Module):
    """Enhanced weighted aggregator with cross-hop and cross-relation attention."""

    def __init__(self, feat_dim: int, hidden_dim: int, num_hops: int, num_relations: int):
        super().__init__()
        self.feat_dim = feat_dim
        self.hidden_dim = hidden_dim
        self.num_hops = num_hops
        self.num_relations = num_relations

        # Cross-hop attention
        self.hop_attention = nn.MultiheadAttention(feat_dim, 4, batch_first=True)

        # Cross-relation attention
        self.relation_attention = nn.MultiheadAttention(feat_dim, 4, batch_first=True)

        # Learnable aggregation weights
        self.hop_weights = nn.Parameter(torch.FloatTensor(num_hops, 1))
        self.relation_weights = nn.Parameter(torch.FloatTensor(num_relations, 1))

        # Output projection
        self.output_proj = nn.Linear(feat_dim, feat_dim)

        self.reset_parameters()

    def reset_parameters(self):
        nn.init.xavier_uniform_(self.hop_weights)
        nn.init.xavier_uniform_(self.relation_weights)
        nn.init.xavier_uniform_(self.output_proj.weight)
        nn.init.zeros_(self.output_proj.bias)

    def forward(self, hop_features: List[torch.Tensor],
                hop_relation_features: List[List[torch.Tensor]]) -> torch.Tensor:
        """
        Enhanced aggregation with cross-hop and cross-relation attention.

        Args:
            hop_features: Features aggregated per hop
            hop_relation_features: Features per hop per relation
        """
        # Cross-hop attention
        stacked_hop_features = torch.stack(hop_features, dim=1)  # [N, num_hops, feat_dim]
        hop_attended, _ = self.hop_attention(
            stacked_hop_features, stacked_hop_features, stacked_hop_features
        )

        # Weighted hop aggregation
        hop_weights = F.softmax(self.hop_weights, dim=0)
        hop_aggregated = torch.sum(hop_attended * hop_weights.T.unsqueeze(-1), dim=1)

        return self.output_proj(hop_aggregated)

    def simple_aggregate(self, features: List[torch.Tensor]) -> torch.Tensor:
        """Simple weighted aggregation for offline mode."""
        stacked_features = torch.stack(features, dim=0)  # [num_hops, N, feat_dim]
        weights = F.softmax(self.hop_weights, dim=0)  # [num_hops, 1]

        aggregated = torch.sum(stacked_features * weights.unsqueeze(-1), dim=0)
        return self.output_proj(aggregated)


class NARSAdapter(nn.Module):
    """Adapter to make NARS compatible with standard interface."""

    def __init__(
        self,
        in_feats: int,
        hidden: int,
        out_feats: int,
        dropout: float,
        input_dropout: float,
        num_layers: int,
        num_etypes: int,
        num_heads: int,
        use_online_computation: bool = True,
    ):
        super().__init__()

        self.nars = NARS(
            in_feats=in_feats,
            hidden=hidden,
            out_feats=out_feats,
            num_hops=num_layers,
            num_relations=num_etypes,
            dropout=dropout,
            use_online_computation=use_online_computation,
            num_attention_heads=num_heads,
        )

        self.linear = nn.Linear(in_feats, in_feats)
        self.label_linear = nn.Linear(out_feats, in_feats)
        self.input_dropout = nn.Dropout(input_dropout)
        self.num_etypes = num_etypes

    def forward(self, graph: dgl.DGLGraph, feat: torch.Tensor, label_feat: torch.Tensor) -> torch.Tensor:
        """Standard interface forward method."""
        # Combine node features and label features
        feat = self.linear(self.input_dropout(feat))
        label_feat = self.label_linear(self.input_dropout(label_feat))
        combined_feat = feat + label_feat

        # Extract relation types from graph
        relation_types = graph.edata.get("_TYPE", torch.zeros(graph.num_edges(), dtype=torch.long))

        # Call NARS with online computation
        return self.nars(graph, combined_feat, relation_types)