import torch
from torch import nn
import torch.nn.functional as F
from .common import FeedForwardNet, WeightedAggregator


class NARS(nn.Module):
    """Simplified implementation of **NARS** (Neighborhood Aggregation over Relation Subsets).

    The core idea is to pre-compute R-hop neighbor-averaged features on various
    *relation induced* subgraphs and then learn *aggregation weights* for each
    hop / relation subset.  In a full system these features are calculated
    offline (see original implementation).  Here we assume they are supplied
    during `forward` as a **list** of tensors `feats`, where `feats[i]` is the
    i-th hop feature (shape: `(N, F)`).
    """

    def __init__(self, in_feats: int, hidden: int, out_feats: int, num_hops: int, dropout: float = 0.5):
        super().__init__()
        self.num_hops = num_hops
        self.input_proj = nn.Linear(in_feats, hidden)
        self.agg = WeightedAggregator(hidden, hidden, num_hops)
        self.post_ffn = FeedForwardNet(hidden, hidden, hidden, 2, dropout)
        self.out_proj = nn.Linear(hidden, out_feats)
        self.dropout = nn.Dropout(dropout)

    def forward(self, feats):
        """`feats` is a *list* containing the pre-computed hop features."""
        assert len(feats) == self.num_hops, "Expect num_hops features"
        feats = [F.relu(self.input_proj(f)) for f in feats]
        agg_feats = self.agg(feats)
        agg_feat = sum(agg_feats) / len(agg_feats)
        out = self.out_proj(self.post_ffn(self.dropout(agg_feat)))
        return out 