import argparse
from curses import meta
import os
import pickle
import random
import warnings
import scipy.sparse as sp
import dgl
from ogb.nodeproppred import DglNodePropPredDataset
from utils import *
from model import *
import torch.nn.functional as F
import dgl.function as fn
import numpy as np
import torch
import time
from tqdm import tqdm
import warnings
from torch.utils.data import Dataset
import torch.distributed as dist
from torch.utils.data.distributed import DistributedSampler

warnings.filterwarnings("ignore")
parser = argparse.ArgumentParser(description='OGBN-MAG (Cluster-RGNN)')
parser.add_argument('--device', type=int, default=0)
parser.add_argument('--num_layer', type=int, default=3)
parser.add_argument('--hidden_channel', type=int, default=512)
parser.add_argument('--dropout', type=float, default=0.5)
parser.add_argument('--lr', type=float, default=0.001)
parser.add_argument('--epochs', type=int, default=300)
parser.add_argument('--batch_size', type=int, default=25000)
parser.add_argument('--runs', type=int, default=1)
parser.add_argument('--save_path', type=str, default="../partition", help=".")
parser.add_argument('--dataset', type=str, default="ogbn-mag", help=".")
parser.add_argument('--num_parts', type=int, default=10, help=".")
parser.add_argument('--R', type=int, default=5, help=".")
parser.add_argument('--use_emb', action='store_true', default=False, help=".")
parser.add_argument('--model', type=str, default="rgat", help=".")
    
args = parser.parse_args()
print(args)

dist.init_process_group(backend='nccl')
torch.cuda.set_device(args.local_rank)


class Mydataset(Dataset):
    def __init__(self, subgraphs):
        self.train = subgraphs
        self.len = len(subgraphs)
 
    def __getitem__(self, item):
        return self.train[item]
 
    def __len__(self):
        return self.len


def load_Freebase(name, device):
    """
    Load dataset and move graph and features to device
    """
    path = "../dataset/Freebase/"
    with open(path + "Freebase.pkl", mode="rb") as f:
        edge_index_dict, train_idx, test_idx, labels, num_nodes_dict = pickle.load(f)
    
    train_idx = torch.tensor(train_idx)
    test_idx = torch.tensor(test_idx)
    labels = torch.tensor(labels)
    new_edge_index_dict = {}
    for key in edge_index_dict.keys():
        new_edge_index_dict[key] = (edge_index_dict[key][0], edge_index_dict[key][1])

    g = dgl.heterograph(new_edge_index_dict)
    for i in range(8):
        g.nodes[f"{i}"].data["feat"] = torch.load(path + f"{i}.pt").float()

    # Map informations to their canonical type.
    n_classes = 7

    target_type_id = g.get_ntype_id("0")
    print(g.get_ntype_id("0"), g.get_ntype_id("1"), g.get_ntype_id("2"), g.get_ntype_id("3"))
    graph = dgl.to_homogeneous(g, ndata=["feat"])
    # graph = dgl.add_reverse_edges(graph, copy_ndata=True, copy_edata=True)
    graph.ndata["target_mask"] = graph.ndata[dgl.NTYPE] == target_type_id
    train_mask = torch.tensor([False for i in range(graph.num_nodes())])
    train_mask[train_idx] = True
    # graph.ndata["train_mask"] = train_mask.unsqueeze(1)
    graph.ndata["train_mask"] = train_mask
    label = torch.zeros((graph.num_nodes(), 1)) - 1
    label[:num_nodes_dict["0"]] = labels.unsqueeze(1)


    evaluator = get_ogb_evaluator("ogbn-mag")
    feat = graph.ndata.pop("feat")
    
    print(f"# Nodes: {graph.number_of_nodes()}\n"
          f"# Edges: {graph.number_of_edges()}\n"
          f"# Train: {len(train_idx)}\n"
          f"# Val: {len(test_idx)}\n"
          f"# Test: {len(test_idx)}\n"
          f"# Classes: {n_classes}")

    return graph, label.long().squeeze(), n_classes, train_idx, test_idx, test_idx, evaluator, num_nodes_dict, feat


def training(subgraphs, test_subgraphs, num_etypes, feat, args, num_classes, labels, num_nodes_dict, disease_offset, train_idx, val_idx, test_idx, label_emb):
    in_feats = feat.shape[-1]

    best_result = []
    for r in range(args.runs):
        if args.model == "rgat":
            model = RGAT(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        elif args.model == "rgcn":
            model = RGCN(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        elif args.model == "rgsn":
            model = RGSN(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        # elif args.model == "rhgnn":
        #     model = RHGNN(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        
        # model = model.to(device)
        print(model)
        model = model.cuda()
        model = torch.nn.parallel.DistributedDataParallel(model, device_ids=[args.local_rank], find_unused_parameters=True)
        print("# Params:", get_n_params(model))
        optimizer = torch.optim.Adam(model.parameters(), lr=args.lr,
                                     weight_decay=0.0)

        print(model.device_ids[0])
        device = f'cuda:{model.device_ids[0]}' if torch.cuda.is_available() else 'cpu'

        best_epoch = 0
        best_val = 0
        best_test = 0
        for epoch in range(1, args.epochs + 1):
            t1 = time.time()
            model.train()
            for batch in subgraphs:
                train_mask = batch.ndata["train_mask"] * batch.ndata["mask"]
                nid = batch.ndata[dgl.NID]
                batch = batch.to(device)
                loss = F.cross_entropy(model(batch, feat[nid].cuda(), label_emb[nid].cuda())[train_mask],
                                       labels[nid][train_mask].cuda())
                optimizer.zero_grad()
                # with amp.scale_loss(loss, optimizer) as scaled_loss:
                #     scaled_loss.backward()
                loss.backward()
                optimizer.step()
            t2 = time.time()
            if epoch % 1 == 0:
                model.eval()
                probs = torch.zeros((num_nodes_dict["0"], num_classes)).cuda()
                for batch in test_subgraphs:
                    target_mask = batch.ndata["target_mask"] * batch.ndata["mask"]
                    nid = batch.ndata[dgl.NID]
                    batch = batch.to(device)
                    probs[nid[target_mask] - disease_offset] += torch.softmax(
                            model(batch, feat[nid].cuda(),
                                         label_emb[nid].cuda())[target_mask], dim=-1).detach()
                all_probs = distributed_sum(probs).cpu()
                # all_probs = probs
                preds = torch.argmax(all_probs, dim=-1)
                train_res = accuracy(preds[train_idx], labels[disease_offset:][train_idx])
                val_res = accuracy(preds[val_idx], labels[disease_offset:][val_idx])
                test_res = accuracy(preds[test_idx], labels[disease_offset:][test_idx])
                t3 = time.time()
                if args.local_rank == 0:
                    log = "Epoch {}, Training Time(s): {:.4f}, Inference Time(s): {:.4f},".format(epoch, t2 - t1, t3 - t2)
                    log += "Acc: Train {:.4f}, Val {:.4f}, Test {:.4f}".format(train_res, val_res, test_res)
                    print(log)
                    if val_res > best_val:
                        best_epoch = epoch
                        best_val = val_res
                        best_test = test_res
                    print("Best Epoch {}, Valid {:.4f}, Test {:.4f}".format(
                            best_epoch, best_val, best_test))
        best_result.append([best_val, best_test])
        if args.local_rank == 0:
            print("Best Epoch {}, Valid {:.4f}, Test {:.4f}".format(
                best_epoch, best_val, best_test))
    if args.local_rank == 0:
        print(best_result)
        best_result = np.array(best_result)
        print("average, val {:.4f}, test {:.4f}".format(np.mean(best_result[:, 0]), np.mean(best_result[:, 1])))


if __name__ == "__main__":
    start = time.time()
    # torch.multiprocessing.set_start_method('spawn')
    # device = f'cuda:{args.device}' if torch.cuda.is_available() else 'cpu'
    
    graph, labels, num_classes, train_idx, val_idx, test_idx, evaluator, num_nodes_dict, feat = load_Freebase(args.dataset,
                                                                                                   None)
    print("loading data costs {:.4f}s".format(time.time() - start))

    offset = [0]
    for i in range(8):
        offset.append(offset[i] + num_nodes_dict[f"{i}"])
    
    limit = graph.num_nodes() // args.num_parts // 5
    if not os.path.exists(args.save_path + f"/Freebase-{args.model}-partition" + str(args.num_parts) + ".pkl"):
        t_1 = time.time()
        adj = graph.adj(scipy_fmt='csr')
        adj = adj.tolil()
        # mag: author, field_of_study, institution, paper
        OO = adj[offset[0]: offset[1], offset[0]: offset[1]]
        OOO = OO.dot(OO)
        O1 = adj[offset[0]: offset[1],
              offset[1]: offset[2]]
        O1O = O1.dot(O1.T)
        O2 = adj[offset[0]: offset[1],
              offset[2]: offset[3]]
        O2O = O2.dot(O2.T)

        O3 = adj[offset[0]: offset[1],
              offset[3]: offset[4]]
        O3O = O3.dot(O3.T)

        O4 = adj[offset[0]: offset[1],
              offset[4]: offset[5]]
        O4O = O4.dot(O4.T)

        O5 = adj[offset[0]: offset[1],
              offset[5]: offset[6]]
        O5O = O5.dot(O5.T)

        O6 = adj[offset[0]: offset[1],
              offset[6]: offset[7]]
        O6O = O6.dot(O6.T)

        O7 = adj[offset[0]: offset[1],
              offset[7]: offset[8]]
        O7O = O7.dot(O7.T)

        O8 = adj[offset[0]: offset[1],
              offset[8]:]
        O8O = O8.dot(O8.T)

        OO_clusters = cluster(OO, args.num_parts,
                               [0, offset[1], offset[-1]],
                               adj, 0)
        OOO_clusters = cluster(OOO, args.num_parts,
                               [0, offset[1], offset[-1]],
                               adj, 0)
        O1O_clusters = cluster(O1O, args.num_parts,
                               [0, offset[1], offset[-1]],
                               adj, 0)
        O2O_clusters = cluster(O2O, args.num_parts,
                               [0, offset[1], offset[-1]],
                               adj, 0)
        O3O_clusters = cluster(O3O, args.num_parts,
                               [0, offset[1], offset[-1]],
                               adj, 0)
        O4O_clusters = cluster(O4O, args.num_parts,
                               [0, offset[1], offset[-1]],
                               adj, 0)
        O5O_clusters = cluster(O5O, args.num_parts,
                               [0, offset[1], offset[-1]],
                               adj, 0)
        O6O_clusters = cluster(O6O, args.num_parts,
                               [0, offset[1], offset[-1]],
                               adj, 0)
        O7O_clusters = cluster(O7O, args.num_parts,
                               [0, offset[1], offset[-1]],
                               adj, 0)
        # O8O_clusters = cluster(O8O, args.num_parts,
        #                        [0, offset[1], offset[-1]],
        #                        adj, 0)
        
        print("num of nodes in each cluster:{}".format(len(OO_clusters[0])))
        OO_influential_nodes = global_clusters(adj, OO_clusters, limit, graph.in_degrees())
        OOO_influential_nodes = global_clusters(adj, OOO_clusters, limit, graph.in_degrees())
        O1O_influential_nodes = global_clusters(adj, O1O_clusters, limit, graph.in_degrees())
        O2O_influential_nodes = global_clusters(adj, O2O_clusters, limit, graph.in_degrees())
        O3O_influential_nodes = global_clusters(adj, O3O_clusters, limit, graph.in_degrees())
        O4O_influential_nodes = global_clusters(adj, O4O_clusters, limit, graph.in_degrees())
        O5O_influential_nodes = global_clusters(adj, O5O_clusters, limit, graph.in_degrees())
        O6O_influential_nodes = global_clusters(adj, O6O_clusters, limit, graph.in_degrees())
        O7O_influential_nodes = global_clusters(adj, O7O_clusters, limit, graph.in_degrees())
        # O8O_influential_nodes = global_clusters(adj, O8O_clusters, limit, graph.in_degrees())

        print("num of nodes in each influential nodes:{}".format(len(OO_influential_nodes[0])))
        print("allocate time:{:.4f}s".format(time.time() - start))
        with open(args.save_path + f"/Freebase-{args.model}-partition" + str(args.num_parts) + ".pkl",
                  mode='wb') as f:
            pickle.dump((OO_clusters, OOO_clusters, O1O_clusters, O2O_clusters, O3O_clusters, 
            O4O_clusters, O5O_clusters, O6O_clusters, O7O_clusters,
                         OO_influential_nodes, OOO_influential_nodes, O1O_influential_nodes,
                         O2O_influential_nodes, O3O_influential_nodes, O4O_influential_nodes,
                          O5O_influential_nodes, O6O_influential_nodes, O7O_influential_nodes), f)
    else:
        with open(args.save_path + f"/Freebase-{args.model}-partition" + str(args.num_parts) + ".pkl",
                  mode='rb') as f:
            OO_clusters, OOO_clusters, O1O_clusters, O2O_clusters, O3O_clusters, \
            O4O_clusters, O5O_clusters, O6O_clusters, O7O_clusters, \
                         d2d_influential_nodes, ddd_influential_nodes, dcd_influential_nodes,\
                         dgd_influential_nodes, O3O_influential_nodes, O4O_influential_nodes,\
                          O5O_influential_nodes, O6O_influential_nodes, O7O_influential_nodes = pickle.load(f)
    
    train_idx = list(set(train_idx.tolist()))
    train_idx.sort()
    train_idx = torch.tensor(train_idx)
    label_emb = torch.zeros((graph.num_nodes(), num_classes))
    mask = torch.tensor([False for i in range(graph.num_nodes())])
    wo_mask_train_idx = torch.tensor([True for i in range(train_idx.shape[0])])
    wo_mask_train_idx[:int(train_idx.shape[0] * 0.5)] = False
    wo_mask_train_idx = wo_mask_train_idx.tolist()
    random.shuffle(wo_mask_train_idx)
    mask[train_idx[wo_mask_train_idx] + offset[0]] = True
    one_hot = F.one_hot(labels[train_idx[wo_mask_train_idx] + offset[0]], num_classes=num_classes)
    label_emb[mask] = one_hot.float()

    metapaths = ["OO", "O3O", "O5O", "O7O"]
    clusters = {"OO": OO_clusters, "OOO": OOO_clusters, "O1O": O1O_clusters, "O2O": O2O_clusters,
    "O3O": O3O_clusters, "O4O": O4O_clusters, "O5O": O5O_clusters, "O6O": O6O_clusters,
    "O7O": O7O_clusters}
    influential_nodes = {"OO": OO_influential_nodes, "OOO": OOO_influential_nodes, "O1O": O1O_influential_nodes,
                         "O2O": O2O_influential_nodes, "O3O": O3O_influential_nodes,"O4O": O4O_influential_nodes,
                         "O5O": O5O_influential_nodes,"O6O": O6O_influential_nodes,"O7O": O7O_influential_nodes
                         }
    subgraphs = []

    cluster_data = [ClusterData(clusters[metapaths[0]], influential_nodes[metapaths[0]], graph),
                    ClusterData(clusters[metapaths[1]], influential_nodes[metapaths[1]], graph),
                    ClusterData(clusters[metapaths[2]], influential_nodes[metapaths[2]], graph),
                    ClusterData(clusters[metapaths[3]], influential_nodes[metapaths[3]], graph),
                    ]
    for i in range(args.num_parts):
        subgraphs.append(cluster_data[0][i])
        subgraphs.append(cluster_data[1][i])
        subgraphs.append(cluster_data[2][i])
        subgraphs.append(cluster_data[3][i])
    print(subgraphs[0].ndata[dgl.NID])
    dataset = Mydataset(subgraphs)
    # train_sampler = torch.utils.data.distributed.DistributedSampler(dataset, shuffle=False)
    train_loader = dgl.dataloading.GraphDataLoader(dataset, batch_size=4, drop_last=False, num_workers=4, use_ddp=True, shuffle=False)
    subgraphs = []
    for i in range(len(cluster_data)):
        for j in range(args.num_parts):
            subgraphs.append(cluster_data[i][j])
    test_dataset = Mydataset(subgraphs)
    # test_sampler = torch.utils.data.distributed.DistributedSampler(test_dataset, shuffle=False)
    test_loader = dgl.dataloading.GraphDataLoader(test_dataset, batch_size=8, drop_last=False, num_workers=4, use_ddp=True, shuffle=False)
    print("training")
    training(train_loader, test_loader, torch.max(graph.edata["_TYPE"]).item() + 1,
            feat, args, num_classes, labels, num_nodes_dict, offset[0], train_idx, val_idx, test_idx, label_emb)
    print("all time:{:.4f}s".format(time.time() - start))