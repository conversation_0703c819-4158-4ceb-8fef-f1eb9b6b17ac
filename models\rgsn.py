import torch
from torch import nn
import torch.nn.functional as F
import dgl
import dgl.function as fn
from .common import FeedForwardNet, FeatAttention, WeightedAggregator


class RGSN_layer(nn.Module):
    def __init__(self, out_feats, num_heads, num_etypes, attn_drop: float = 0.2):
        super().__init__()
        self._out_feats = out_feats
        self._num_etypes = num_etypes
        self._num_heads = num_heads
        self.attn_dropout = nn.Dropout(attn_drop)
        self.leaky_relu = nn.LeakyReLU(0.2)
        self.attn_layer = FeatAttention(out_feats, num_heads, attn_drop)
        self.intra_attn_l = nn.Parameter(torch.Tensor(num_etypes, 1, num_heads, out_feats))
        self.intra_attn_r = nn.Parameter(torch.Tensor(num_etypes, 1, num_heads, out_feats))
        self.reset_parameters()

    def reset_parameters(self):
        gain = nn.init.calculate_gain("relu")
        nn.init.xavier_normal_(self.intra_attn_l, gain=gain)
        nn.init.xavier_normal_(self.intra_attn_r, gain=gain)

    def forward(self, graph: dgl.DGLGraph, feat: torch.Tensor):
        with graph.local_scope():
            feat = F.normalize(feat)
            feat_skip = [feat.view(-1, self._num_heads, self._out_feats)]
            graph.ndata["feat"] = feat.view(-1, self._num_heads, self._out_feats)
            for i in range(self._num_etypes):
                mask = graph.edata["_TYPE"] == i
                subgraph = graph.edge_subgraph(mask, preserve_nodes=True)
                graph = dgl.add_self_loop(graph)
                subgraph.update_all(fn.copy_u("feat", "msg"), fn.mean("msg", "feat_neighbor"))
                graph = dgl.remove_self_loop(graph)

                subgraph.ndata["left"] = (subgraph.ndata["feat"] * self.intra_attn_l[i]).sum(-1).unsqueeze(-1)
                subgraph.ndata["right"] = (subgraph.ndata["feat_neighbor"] * self.intra_attn_r[i]).sum(-1).unsqueeze(-1)
                subgraph.apply_edges(fn.u_add_v("left", "right", "a"))
                subgraph.edata["a"] = self.leaky_relu(subgraph.edata["a"].unsqueeze(-1))
                subgraph.edata["a"] = dgl.nn.functional.edge_softmax(subgraph, subgraph.edata["a"], norm_by="dst")
                subgraph.edata["a"] = self.attn_dropout(subgraph.edata["a"]).view(-1, self._num_heads, 1)
                subgraph.update_all(fn.u_mul_e("feat", "a", "msg"), fn.sum("msg", "feat_neighbor"))
                feat_skip.append(subgraph.ndata.pop("feat_neighbor"))
            feat = self.attn_layer(feat_skip).view(-1, self._num_heads * self._out_feats)
            return F.relu(feat)


class RGSN(nn.Module):
    """Relation-aware Graph Self-attention Network"""

    def __init__(
        self,
        in_feats: int,
        hidden: int,
        out_feats: int,
        dropout: float,
        input_dropout: float,
        num_layers: int,
        num_etypes: int,
        num_heads: int,
    ):
        super().__init__()
        self.num_layers = num_layers
        self.out_feats = out_feats
        self.hidden = hidden
        self.num_heads = num_heads

        self.input_dropout = nn.Dropout(input_dropout)
        self.dropout = nn.Dropout(dropout)

        self.layers = nn.ModuleList([
            RGSN_layer(hidden // num_heads, num_heads, num_etypes) for _ in range(num_layers)
        ])

        self.mlp = nn.Sequential(
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, out_feats),
        )

        self.linear = nn.Linear(in_feats, hidden)
        self.label_linear = nn.Linear(out_feats, hidden)
        self.norms = nn.ModuleList([nn.BatchNorm1d(hidden) for _ in range(num_layers + 1)])
        self.inception_ffs = nn.ModuleList([
            FeedForwardNet(hidden, hidden, hidden, 1, dropout) for _ in range(num_layers + 1)
        ])

    def forward(self, graph: dgl.DGLGraph, feat: torch.Tensor, label_feat: torch.Tensor):
        feat = self.linear(self.input_dropout(feat))
        label_feat = self.label_linear(self.input_dropout(label_feat))
        feat = feat + label_feat

        feat_hop = [feat]
        for layer in self.layers:
            feat = layer(graph, feat)
            feat_hop.append(feat)
        feat_hop = [self.input_dropout(f) for f in feat_hop]

        norm_feats = [bn(f) for f, bn in zip(feat_hop, self.norms)]
        tmp = [ff(f) for f, ff in zip(norm_feats, self.inception_ffs)]
        feat = self.dropout((sum(tmp) / len(tmp)).view(-1, self.hidden))
        return self.mlp(feat) 