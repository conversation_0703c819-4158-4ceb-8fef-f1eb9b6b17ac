#!/usr/bin/env bash

#--- Multi-nodes training hyperparams ---
nnodes=2
master_addr="Your address"
master_port="Your port"
nproc_per_node=2
node_rank=0
model="rgat"


# Node Classification on Ogbn-mag
python -m torch.distributed.launch --master_port ${master_port} --nproc_per_node=${nproc_per_node} \
            --nnodes=${nnodes} --node_rank=${node_rank}  \
            --master_addr=${master_addr} \
            train_rgnn_mag.py  --use_emb --model ${model}

# Node Classification on DBLP
python -m torch.distributed.launch --master_port ${master_port} --nproc_per_node=${nproc_per_node} \
            --nnodes=${nnodes} --node_rank=${node_rank}  \
            --master_addr=${master_addr} \
            train_rgnn_DBLP.py  --use_emb --model ${model}
        
# Node Classification on Freebase
python -m torch.distributed.launch --master_port ${master_port} --nproc_per_node=${nproc_per_node} \
            --nnodes=${nnodes} --node_rank=${node_rank}  \
            --master_addr=${master_addr} \
            train_rgnn_Freebase.py  --use_emb --model ${model}