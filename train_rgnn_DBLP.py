import argparse
from curses import meta
import os
import pickle
import random
import warnings
import scipy.sparse as sp
import dgl
from ogb.nodeproppred import DglNodePropPredDataset
from utils import *
from model import *
import torch.nn.functional as F
import dgl.function as fn
import numpy as np
import torch
import time
from tqdm import tqdm
import multiprocessing
import warnings
from torch.utils.data import Dataset
import torch.distributed as dist
from torch.utils.data.distributed import DistributedSampler


warnings.filterwarnings("ignore")
parser = argparse.ArgumentParser(description='OGBN-MAG (Cluster-RGNN)')
parser.add_argument('--device', type=int, default=0)
parser.add_argument('--num_layer', type=int, default=3)
parser.add_argument('--hidden_channel', type=int, default=512)
parser.add_argument('--dropout', type=float, default=0.5)
parser.add_argument('--lr', type=float, default=0.001)
parser.add_argument('--epochs', type=int, default=300)
parser.add_argument('--batch_size', type=int, default=25000)
parser.add_argument('--runs', type=int, default=1)
parser.add_argument('--save_path', type=str, default="../partition", help=".")
parser.add_argument('--dataset', type=str, default="ogbn-mag", help=".")
parser.add_argument('--num_parts', type=int, default=100, help=".")
parser.add_argument('--R', type=int, default=5, help=".")
parser.add_argument('--use_emb', action='store_true', default=False, help=".")
parser.add_argument('--model', type=str, default="rgat", help=".")
args = parser.parse_args()
print(args)
dist.init_process_group(backend='nccl')
torch.cuda.set_device(args.local_rank)


class Mydataset(Dataset):
    def __init__(self, subgraphs):
        self.train = subgraphs
        self.len = len(subgraphs)
 
    def __getitem__(self, item):
        return self.train[item]
 
    def __len__(self):
        return self.len


def load_DBLP(name, device):
    """
    Load dataset and move graph and features to device
    """
    path = "../dataset/DBLP/"
    with open(path + "DBLP.pkl", mode="rb") as f:
        edge_index_dict, feature, train_idx, test_idx, labels, num_nodes_dict = pickle.load(f)

    train_idx = torch.tensor(train_idx)
    test_idx = torch.tensor(test_idx)
    num_phrase = 217557
    num_author = 1766361
    num_venue = 5076
    num_year = 83
    idx_author = num_phrase
    idx_venue = num_phrase + num_author
    idx_year = num_phrase + num_author + num_venue
    num_nodes = num_phrase + num_author + num_venue + num_year
    phrase_emb = feature["phrase"]
    author_emb = feature["author"]
    venue_emb = feature["venue"]
    year_emb = feature["year"]

    new_edge_index_dict = {}
    for key in edge_index_dict.keys():
        new_edge_index_dict[key] = (edge_index_dict[key][0], edge_index_dict[key][1])

    g = dgl.heterograph(new_edge_index_dict)
    g.nodes["author"].data["feat"] = author_emb
    g.nodes["phrase"].data["feat"] = phrase_emb
    g.nodes["venue"].data["feat"] = venue_emb
    g.nodes["year"].data["feat"] = year_emb

    # Map informations to their canonical type.
    n_classes = 13
    
    target_type_id = g.get_ntype_id("author")
    print(g.get_ntype_id("author"), g.get_ntype_id("phrase"), g.get_ntype_id("venue"), g.get_ntype_id("year"))
    graph = dgl.to_homogeneous(g, ndata=["feat"])
    # graph = dgl.add_reverse_edges(graph, copy_ndata=True, copy_edata=True)
    graph.ndata["target_mask"] = graph.ndata[dgl.NTYPE] == target_type_id
    train_mask = torch.tensor([False for i in range(graph.num_nodes())])
    train_mask[train_idx] = True
    # graph.ndata["train_mask"] = train_mask.unsqueeze(1)
    graph.ndata["train_mask"] = train_mask
    label = torch.zeros((graph.num_nodes(), 1)) - 1
    label[:num_author] = labels.unsqueeze(1)
    graph.ndata["label"] = label.long().squeeze()

    evaluator = get_ogb_evaluator("ogbn-mag")
    NID = torch.tensor([0 for i in range(graph.num_nodes())])
    NID[:num_author] = torch.tensor([i for i in range(num_nodes_dict["author"])])
    graph.ndata["ID"] = NID
    feat = graph.ndata.pop("feat")
    
    print(f"# Nodes: {graph.number_of_nodes()}\n"
          f"# Edges: {graph.number_of_edges()}\n"
          f"# Train: {len(train_idx)}\n"
          f"# Val: {len(test_idx)}\n"
          f"# Test: {len(test_idx)}\n"
          f"# Classes: {n_classes}")

    return graph, label.long().squeeze(), n_classes, train_idx, test_idx, test_idx, evaluator, num_nodes_dict, feat


def training(subgraphs, test_subgraphs, num_etypes, feat, args, num_classes, labels, num_nodes_dict, author_offset, train_idx, val_idx, test_idx, label_emb):
    in_feats = feat.shape[-1]

    best_result = []
    for r in range(args.runs):
        if args.model == "rgat":
            model = RGAT(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        elif args.model == "rgcn":
            model = RGCN(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        elif args.model == "rgsn":
            model = RGSN(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        # elif args.model == "rhgnn":
        #     model = RHGNN(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        # model = model.to(device)

        print(model)
        model = model.cuda()
        model = torch.nn.parallel.DistributedDataParallel(model, device_ids=[args.local_rank], find_unused_parameters=True)
        print("# Params:", get_n_params(model))
        optimizer = torch.optim.Adam(model.parameters(), lr=args.lr,
                                     weight_decay=0.0)
        
        print(model.device_ids[0])
        device = f'cuda:{model.device_ids[0]}' if torch.cuda.is_available() else 'cpu'
        
        best_epoch = 0
        best_val = 0
        best_test = 0
        for epoch in range(1, args.epochs + 1):
            t1 = time.time()
            model.train()
            for batch in subgraphs:
                train_mask = batch.ndata["train_mask"] * batch.ndata["mask"]
                nid = batch.ndata[dgl.NID]
                batch = batch.to(device)
                loss = F.cross_entropy(model(batch, feat[nid].cuda(), label_emb[nid].cuda())[train_mask],
                                       labels[nid][train_mask].cuda())
                optimizer.zero_grad()
                # with amp.scale_loss(loss, optimizer) as scaled_loss:
                #     scaled_loss.backward()
                loss.backward()
                optimizer.step()
            t2 = time.time()
            if epoch % 1 == 0:
                model.eval()
                probs = torch.zeros((num_nodes_dict["author"], num_classes)).cuda()
                for batch in test_subgraphs:
                    target_mask = batch.ndata["target_mask"] * batch.ndata["mask"]
                    nid = batch.ndata[dgl.NID]
                    batch = batch.to(device)
                    probs[nid[target_mask] - author_offset] += torch.softmax(
                            model(batch, feat[nid].cuda(),
                                         label_emb[nid].cuda())[target_mask], dim=-1).detach()
                all_probs = distributed_sum(probs).cpu()
                # all_probs = probs
                preds = torch.argmax(all_probs, dim=-1)
                train_res = accuracy(preds[train_idx], labels[author_offset:][train_idx])
                val_res = accuracy(preds[val_idx], labels[author_offset:][val_idx])
                test_res = accuracy(preds[test_idx], labels[author_offset:][test_idx])
                t3 = time.time()
                if args.local_rank == 0:
                    log = "Epoch {}, Training Time(s): {:.4f}, Inference Time(s): {:.4f},".format(epoch, t2 - t1, t3 - t2)
                    log += "Acc: Train {:.4f}, Val {:.4f}, Test {:.4f}".format(train_res, val_res, test_res)
                    print(log)
                    if val_res > best_val:
                        best_epoch = epoch
                        best_val = val_res
                        best_test = test_res
                    print("Best Epoch {}, Valid {:.4f}, Test {:.4f}".format(
                            best_epoch, best_val, best_test))
        best_result.append([best_val, best_test])
        if args.local_rank == 0:
            print("Best Epoch {}, Valid {:.4f}, Test {:.4f}".format(
                best_epoch, best_val, best_test))
    if args.local_rank == 0:
        print(best_result)
        best_result = np.array(best_result)
        print("average, val {:.4f}, test {:.4f}".format(np.mean(best_result[:, 0]), np.mean(best_result[:, 1])))

if __name__ == "__main__":
    start = time.time()
    # torch.multiprocessing.set_start_method('spawn')
    # device = f'cuda:{args.device}' if torch.cuda.is_available() else 'cpu'
    
    graph, labels, num_classes, train_idx, val_idx, test_idx, evaluator, num_nodes_dict, feat = load_DBLP(args.dataset,
                                                                                                   None)
    print("loading data costs {:.4f}s".format(time.time() - start))

    author_offset = 0
    phrase_offset = author_offset + num_nodes_dict["author"]
    venue_offset = phrase_offset + num_nodes_dict["phrase"]
    year_offset = venue_offset + num_nodes_dict["venue"]
    node_offset = torch.tensor([author_offset for i in range(num_nodes_dict["author"])] +
                               [phrase_offset for i in range(num_nodes_dict["phrase"])] +
                               [venue_offset for i in range(num_nodes_dict["venue"])] +
                               [year_offset for i in range(num_nodes_dict["year"])])
    limit = graph.num_nodes() // args.num_parts // 10
    if not os.path.exists(args.save_path + f"/DBLP-{args.model}-partition" + str(args.num_parts) + ".pkl"):
        t_1 = time.time()
        adj = graph.adj(scipy_fmt='csr')
        adj = adj.tolil()
        # mag: author, field_of_study, institution, paper
        a2a = adj[author_offset: author_offset + num_nodes_dict["author"],
              author_offset: author_offset + num_nodes_dict["author"]]
        print(a2a.shape)
        ap = adj[author_offset: author_offset + num_nodes_dict["author"],
             phrase_offset: phrase_offset + num_nodes_dict["phrase"]]
        pap = ap.T.dot(ap)

        av = adj[author_offset: author_offset + num_nodes_dict["author"],
              venue_offset: venue_offset + num_nodes_dict["venue"]]
        vav = av.T.dot(av)
        ay = adj[author_offset: author_offset + num_nodes_dict["author"],
             year_offset: year_offset + num_nodes_dict["year"]]
        yay = ay.T.dot(ay)

        a2a_clusters = cluster(a2a, args.num_parts,
                               [0, phrase_offset, year_offset + num_nodes_dict["year"]],
                               adj, 0)
        apa_clusters = cluster(pap, args.num_parts,
                               [0, phrase_offset, phrase_offset + num_nodes_dict["phrase"], year_offset + num_nodes_dict["year"]],
                               adj, 1)
        ava_clusters = cluster(vav, args.num_parts,
                               [0, venue_offset, venue_offset + num_nodes_dict["venue"], year_offset + num_nodes_dict["year"]],
                               adj, 1)

        aya_clusters = cluster(yay, args.num_parts,
                               [0, year_offset, year_offset + num_nodes_dict["year"]],
                               adj, 1)
        print("num of nodes in each cluster:{}".format(len(a2a_clusters[0])))
        a2a_influential_nodes = global_clusters(adj, a2a_clusters, limit, graph.in_degrees())
        apa_influential_nodes = global_clusters(adj, apa_clusters, limit, graph.in_degrees())
        ava_influential_nodes = global_clusters(adj, ava_clusters, limit, graph.in_degrees())
        aya_influential_nodes = global_clusters(adj, aya_clusters, limit, graph.in_degrees())
        
        print("num of nodes in each influential nodes:{}".format(len(a2a_influential_nodes[0])))
        print("allocate time:{:.4f}s".format(time.time() - start))
        with open(args.save_path + f"/DBLP-{args.model}-partition" + str(args.num_parts) + ".pkl",
                  mode='wb') as f:
            pickle.dump((a2a_clusters, apa_clusters, ava_clusters, aya_clusters,
                         a2a_influential_nodes, apa_influential_nodes, ava_influential_nodes, aya_influential_nodes), f)
    else:
        with open(args.save_path + f"/DBLP-{args.model}-partition" + str(args.num_parts) + ".pkl",
                  mode='rb') as f:
            a2a_clusters, apa_clusters, ava_clusters, aya_clusters, \
                         a2a_influential_nodes, apa_influential_nodes, ava_influential_nodes, aya_influential_nodes = pickle.load(f)
    
    label_emb = torch.zeros((graph.num_nodes(), num_classes))
    mask = torch.tensor([False for i in range(graph.num_nodes())])
    train_idx = list(set(train_idx.tolist()))
    train_idx.sort()
    train_idx = torch.tensor(train_idx)
    
    wo_mask_train_idx = torch.tensor([True for i in range(train_idx.shape[0])])
    wo_mask_train_idx[:int(train_idx.shape[0] * 0.5)] = False
    wo_mask_train_idx = wo_mask_train_idx.tolist()
    random.shuffle(wo_mask_train_idx)
    mask[train_idx[wo_mask_train_idx] + author_offset] = True
    one_hot = F.one_hot(labels[train_idx[wo_mask_train_idx] + author_offset], num_classes=num_classes)
    label_emb[mask] = one_hot.float()
    # print(accuracy(torch.argmax(label_emb[mask], dim=-1), labels[train_idx[wo_mask_train_idx] + author_offset]))
    # print(accuracy(torch.argmax(label_emb[train_idx + author_offset], dim=-1), labels[train_idx + author_offset]))
    # graph.ndata["label_emb"] = label_emb
    
    metapaths = ["aa", "apa"]
    clusters = {"aa": a2a_clusters, "apa": apa_clusters, "ava": ava_clusters, "aya": aya_clusters}
    influential_nodes = {"aa": a2a_influential_nodes, "apa": apa_influential_nodes, "ava": ava_influential_nodes, "aya": aya_influential_nodes}
    
    subgraphs = []

    cluster_data = [ClusterData(clusters[metapaths[0]], influential_nodes[metapaths[0]], graph),
                    ClusterData(clusters[metapaths[1]], influential_nodes[metapaths[1]], graph)]
    for i in range(args.num_parts):
        subgraphs.append(cluster_data[0][i])
        subgraphs.append(cluster_data[1][i])
    print(subgraphs[0].ndata[dgl.NID])
    dataset = Mydataset(subgraphs)
    # train_sampler = torch.utils.data.distributed.DistributedSampler(dataset, shuffle=False)
    train_loader = dgl.dataloading.GraphDataLoader(dataset, batch_size=2, drop_last=False, num_workers=4, use_ddp=True, shuffle=False)
    subgraphs = []
    for i in range(len(cluster_data)):
        for j in range(args.num_parts):
            subgraphs.append(cluster_data[i][j])
    test_dataset = Mydataset(subgraphs)
    # test_sampler = torch.utils.data.distributed.DistributedSampler(test_dataset, shuffle=False)
    test_loader = dgl.dataloading.GraphDataLoader(test_dataset, batch_size=8, drop_last=False, num_workers=4, use_ddp=True, shuffle=False)
    print("training")
    training(train_loader, test_loader, torch.max(graph.edata["_TYPE"]).item() + 1,
            feat, args, num_classes, labels, num_nodes_dict, author_offset, train_idx, val_idx, test_idx, label_emb)
    print("all time:{:.4f}s".format(time.time() - start))
