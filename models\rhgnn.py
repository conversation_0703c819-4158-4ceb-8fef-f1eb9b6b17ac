import torch
from torch import nn
import torch.nn.functional as F
import dgl
from dgl.nn import HeteroGraphConv, GATConv
from .common import FeedForwardNet


class RHGNN(nn.Module):
    """Relation-aware Heterogeneous Graph Neural Network (简化实现).

    1. 每种节点类型使用独立的线性变换对输入特征升维到 *hidden* 维。
    2. 采用 `HeteroGraphConv` 封装若干 `GATConv`（关系级别可学习注意力），
       支持不同关系 etype 共享隐层维度。
    3. **多层** 堆叠，每层结束后进行层归一化与 Dropout。
    4. 只返回 *target_ntype* 的输出向量，用于节点分类等下游任务。
    """

    def __init__(
        self,
        in_feats: int,
        hidden: int,
        out_feats: int,
        dropout: float,
        input_dropout: float,
        num_layers: int,
        canonical_etypes,
        target_ntype: str,
        num_heads: int = 4,
    ):
        super().__init__()
        self.target_ntype = target_ntype
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.hidden = hidden

        # 预处理 —— 节点线性映射
        self.input_linears = nn.ModuleDict()
        self.ntypes = list({ntype for src, _, dst in canonical_etypes for ntype in (src, dst)})
        for ntype in self.ntypes:
            self.input_linears[ntype] = nn.Linear(in_feats, hidden)

        # 关系注意力卷积层
        self.convs = nn.ModuleList()
        for _ in range(num_layers):
            conv = HeteroGraphConv(
                {
                    etype: GATConv(
                        in_feats=hidden,
                        out_feats=hidden // num_heads,
                        num_heads=num_heads,
                        feat_drop=dropout,
                        attn_drop=dropout,
                        allow_zero_in_degree=True,
                    )
                    for etype in canonical_etypes
                },
                aggregate="mean",
            )
            self.convs.append(conv)

        self.dropout = nn.Dropout(dropout)
        # per-layer FFN after concat heads
        self.post_ffn = nn.ModuleList([
            FeedForwardNet(hidden, hidden, hidden, 1, dropout) for _ in range(num_layers)
        ])

        self.out_proj = nn.Linear(hidden, out_feats)

    # ------------------------------------------------------------------
    # Forward
    # ------------------------------------------------------------------
    def forward(self, g: dgl.DGLHeteroGraph, feat_dict):
        # 初始特征映射
        h_dict = {ntype: F.relu(self.input_linears[ntype](feat)) for ntype, feat in feat_dict.items()}
        h_dict = {k: self.dropout(v) for k, v in h_dict.items()}

        # 多层异构卷积
        for conv, ffn in zip(self.convs, self.post_ffn):
            h_dict = conv(g, h_dict)
            # GATConv 输出形状 (N, num_heads, hidden_per_head)
            h_dict = {k: F.relu(v.mean(1)) for k, v in h_dict.items()}
            h_dict = {k: ffn(self.dropout(v)) for k, v in h_dict.items()}

        return self.out_proj(h_dict[self.target_ntype]) 