import torch
from torch import nn
import torch.nn.functional as F
import dgl
import dgl.function as fn
from dgl.nn import HeteroGraphConv
from .common import FeedForwardNet
import math


class RelationAwareAttention(nn.Module):
    """Relation-aware attention mechanism for heterogeneous graphs."""

    def __init__(self, in_feats: int, out_feats: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        self.in_feats = in_feats
        self.out_feats = out_feats
        self.num_heads = num_heads
        self.head_dim = out_feats // num_heads

        assert out_feats % num_heads == 0, "out_feats must be divisible by num_heads"

        # Linear transformations for query, key, value
        self.W_q = nn.Linear(in_feats, out_feats, bias=False)
        self.W_k = nn.Linear(in_feats, out_feats, bias=False)
        self.W_v = nn.Linear(in_feats, out_feats, bias=False)

        # Relation-specific transformations
        self.W_rel = nn.Parameter(torch.FloatTensor(out_feats, out_feats))

        # Attention parameters
        self.attn_l = nn.Parameter(torch.FloatTensor(1, num_heads, self.head_dim))
        self.attn_r = nn.Parameter(torch.FloatTensor(1, num_heads, self.head_dim))

        self.dropout = nn.Dropout(dropout)
        self.leaky_relu = nn.LeakyReLU(0.2)

        self.reset_parameters()

    def reset_parameters(self):
        gain = nn.init.calculate_gain('relu')
        nn.init.xavier_uniform_(self.W_q.weight, gain=gain)
        nn.init.xavier_uniform_(self.W_k.weight, gain=gain)
        nn.init.xavier_uniform_(self.W_v.weight, gain=gain)
        nn.init.xavier_uniform_(self.W_rel, gain=gain)
        nn.init.xavier_uniform_(self.attn_l, gain=gain)
        nn.init.xavier_uniform_(self.attn_r, gain=gain)

    def forward(self, graph, feat_src, feat_dst, edge_type_emb=None):
        """
        Args:
            graph: DGL graph
            feat_src: Source node features [N_src, in_feats]
            feat_dst: Destination node features [N_dst, in_feats]
            edge_type_emb: Edge type embeddings [N_edges, out_feats]
        """
        with graph.local_scope():
            # Transform features
            q = self.W_q(feat_dst).view(-1, self.num_heads, self.head_dim)  # [N_dst, H, D]
            k = self.W_k(feat_src).view(-1, self.num_heads, self.head_dim)  # [N_src, H, D]
            v = self.W_v(feat_src).view(-1, self.num_heads, self.head_dim)  # [N_src, H, D]

            # Store in graph
            graph.srcdata['k'] = k
            graph.srcdata['v'] = v
            graph.dstdata['q'] = q

            # Compute attention scores
            graph.apply_edges(self._edge_attention)

            # Apply relation-aware transformation if edge type embeddings provided
            if edge_type_emb is not None:
                rel_transform = torch.matmul(edge_type_emb, self.W_rel)
                graph.edata['rel_transform'] = rel_transform.view(-1, self.num_heads, self.head_dim)
                graph.apply_edges(self._apply_relation_transform)

            # Softmax attention weights
            graph.edata['a'] = dgl.nn.functional.edge_softmax(graph, graph.edata['a'])
            graph.edata['a'] = self.dropout(graph.edata['a'])

            # Message passing
            graph.update_all(fn.u_mul_e('v', 'a', 'm'), fn.sum('m', 'h'))

            return graph.dstdata['h'].view(-1, self.out_feats)

    def _edge_attention(self, edges):
        # Compute attention scores: a = LeakyReLU(attn_l^T * q + attn_r^T * k)
        a_l = (edges.dst['q'] * self.attn_l).sum(dim=-1, keepdim=True)  # [E, H, 1]
        a_r = (edges.src['k'] * self.attn_r).sum(dim=-1, keepdim=True)  # [E, H, 1]
        a = self.leaky_relu(a_l + a_r)  # [E, H, 1]
        return {'a': a}

    def _apply_relation_transform(self, edges):
        # Apply relation-specific transformation to values
        v_rel = edges.src['v'] + edges.data['rel_transform']
        return {'v': v_rel}


class HeteroNodeTypeEmbedding(nn.Module):
    """Node type embedding for heterogeneous graphs."""

    def __init__(self, ntypes: list, embed_dim: int):
        super().__init__()
        self.ntypes = ntypes
        self.embed_dim = embed_dim
        self.type_embeddings = nn.ModuleDict({
            ntype: nn.Embedding(1, embed_dim) for ntype in ntypes
        })

    def forward(self, ntype: str, num_nodes: int):
        """Get type embedding for nodes of given type."""
        type_idx = torch.zeros(num_nodes, dtype=torch.long, device=next(self.parameters()).device)
        return self.type_embeddings[ntype](type_idx)


class RHGNNLayer(nn.Module):
    """Single layer of RHGNN with relation-aware attention."""

    def __init__(self, in_feats: int, out_feats: int, canonical_etypes: list,
                 num_heads: int = 4, dropout: float = 0.1, use_norm: bool = True):
        super().__init__()
        self.in_feats = in_feats
        self.out_feats = out_feats
        self.canonical_etypes = canonical_etypes
        self.num_heads = num_heads
        self.use_norm = use_norm

        # Relation-aware attention for each edge type
        self.rel_attns = nn.ModuleDict()
        for src_type, etype, dst_type in canonical_etypes:
            self.rel_attns[etype] = RelationAwareAttention(
                in_feats, out_feats, num_heads, dropout
            )

        # Edge type embeddings
        self.edge_type_embed = nn.Embedding(len(canonical_etypes), out_feats)

        # Normalization and activation
        if use_norm:
            self.norm = nn.LayerNorm(out_feats)
        self.dropout = nn.Dropout(dropout)

        # Residual connection projection
        if in_feats != out_feats:
            self.residual_proj = nn.Linear(in_feats, out_feats)
        else:
            self.residual_proj = nn.Identity()

    def forward(self, g: dgl.DGLHeteroGraph, feat_dict: dict):
        """Forward pass of RHGNN layer."""
        new_feat_dict = {}

        for ntype in feat_dict:
            # Initialize with residual connection
            new_feat_dict[ntype] = self.residual_proj(feat_dict[ntype])

        # Process each edge type
        for i, (src_type, etype, dst_type) in enumerate(self.canonical_etypes):
            if etype in g.etypes and src_type in feat_dict and dst_type in feat_dict:
                # Get subgraph for this edge type
                subg = g[src_type, etype, dst_type]

                if subg.num_edges() > 0:
                    # Get edge type embeddings
                    edge_type_emb = self.edge_type_embed(
                        torch.full((subg.num_edges(),), i,
                                 dtype=torch.long, device=feat_dict[src_type].device)
                    )

                    # Apply relation-aware attention
                    h_dst = self.rel_attns[etype](
                        subg, feat_dict[src_type], feat_dict[dst_type], edge_type_emb
                    )

                    # Aggregate with existing features
                    new_feat_dict[dst_type] = new_feat_dict[dst_type] + h_dst

        # Apply normalization and activation
        for ntype in new_feat_dict:
            if self.use_norm:
                new_feat_dict[ntype] = self.norm(new_feat_dict[ntype])
            new_feat_dict[ntype] = F.relu(self.dropout(new_feat_dict[ntype]))

        return new_feat_dict


class RHGNN(nn.Module):
    """Complete Relation-aware Heterogeneous Graph Neural Network.

    This implementation includes:
    1. Relation-aware attention mechanisms for different edge types
    2. Node type embeddings for heterogeneous node handling
    3. Multi-layer message passing with residual connections
    4. Layer normalization and proper regularization
    5. Adaptive aggregation across different relation types
    6. Support for both homogeneous and heterogeneous input features
    """

    def __init__(
        self,
        in_feats: int,
        hidden: int,
        out_feats: int,
        dropout: float,
        input_dropout: float,
        num_layers: int,
        canonical_etypes: list,
        target_ntype: str,
        num_heads: int = 4,
        use_node_type_embed: bool = True,
        node_type_embed_dim: int = 64,
        use_residual: bool = True,
        use_layer_norm: bool = True,
    ):
        super().__init__()
        self.target_ntype = target_ntype
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.hidden = hidden
        self.canonical_etypes = canonical_etypes
        self.use_node_type_embed = use_node_type_embed
        self.use_residual = use_residual

        # Extract all node types
        self.ntypes = list({ntype for src, _, dst in canonical_etypes for ntype in (src, dst)})

        # Input feature transformation for each node type
        self.input_linears = nn.ModuleDict()
        input_dim = in_feats
        if use_node_type_embed:
            input_dim += node_type_embed_dim
            self.node_type_embed = HeteroNodeTypeEmbedding(self.ntypes, node_type_embed_dim)

        for ntype in self.ntypes:
            self.input_linears[ntype] = nn.Linear(input_dim, hidden)

        # Multi-layer RHGNN
        self.layers = nn.ModuleList()
        for i in range(num_layers):
            layer_in_feats = hidden
            layer_out_feats = hidden

            self.layers.append(RHGNNLayer(
                layer_in_feats, layer_out_feats, canonical_etypes,
                num_heads, dropout, use_layer_norm
            ))

        # Global attention for aggregating multi-layer representations
        self.layer_attention = nn.MultiheadAttention(
            hidden, num_heads, dropout=dropout, batch_first=True
        )

        # Output projection layers
        self.output_layers = nn.ModuleList([
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, hidden),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden, out_feats)
        ])

        # Regularization
        self.input_dropout = nn.Dropout(input_dropout)
        self.dropout = nn.Dropout(dropout)

        # Initialize parameters
        self.reset_parameters()

    def reset_parameters(self):
        """Initialize model parameters."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.xavier_uniform_(module.weight)

    def forward(self, g: dgl.DGLHeteroGraph, feat_dict: dict):
        """Forward pass of RHGNN.

        Args:
            g: Heterogeneous DGL graph
            feat_dict: Dictionary of node features {ntype: features}

        Returns:
            Output features for target node type
        """
        # Input feature transformation with optional node type embeddings
        h_dict = {}
        for ntype, feat in feat_dict.items():
            if ntype in self.ntypes:
                # Apply input dropout
                feat = self.input_dropout(feat)

                # Add node type embeddings if enabled
                if self.use_node_type_embed:
                    type_emb = self.node_type_embed(ntype, feat.size(0))
                    feat = torch.cat([feat, type_emb], dim=-1)

                # Linear transformation
                h_dict[ntype] = F.relu(self.input_linears[ntype](feat))

        # Store layer representations for skip connections
        layer_outputs = [h_dict]

        # Multi-layer message passing
        for layer in self.layers:
            h_dict = layer(g, h_dict)
            layer_outputs.append({ntype: h.clone() for ntype, h in h_dict.items()})

        # Aggregate multi-layer representations using attention
        if len(layer_outputs) > 1 and self.target_ntype in h_dict:
            target_reprs = []
            for layer_out in layer_outputs:
                if self.target_ntype in layer_out:
                    target_reprs.append(layer_out[self.target_ntype])

            if len(target_reprs) > 1:
                # Stack representations: [num_layers+1, num_nodes, hidden]
                stacked_reprs = torch.stack(target_reprs, dim=0).transpose(0, 1)

                # Apply self-attention across layers
                attended_repr, _ = self.layer_attention(
                    stacked_reprs, stacked_reprs, stacked_reprs
                )

                # Average across layers
                h_dict[self.target_ntype] = attended_repr.mean(dim=1)

        # Output projection
        if self.target_ntype in h_dict:
            h = h_dict[self.target_ntype]
            for layer in self.output_layers:
                h = layer(h)
            return h
        else:
            raise ValueError(f"Target node type '{self.target_ntype}' not found in graph")

    def get_attention_weights(self, g: dgl.DGLHeteroGraph, feat_dict: dict):
        """Get attention weights for interpretability."""
        attention_weights = {}

        # Set model to evaluation mode
        was_training = self.training
        self.eval()

        with torch.no_grad():
            # Process input features
            h_dict = {}
            for ntype, feat in feat_dict.items():
                if ntype in self.ntypes:
                    if self.use_node_type_embed:
                        type_emb = self.node_type_embed(ntype, feat.size(0))
                        feat = torch.cat([feat, type_emb], dim=-1)
                    h_dict[ntype] = F.relu(self.input_linears[ntype](feat))

            # Extract attention weights from each layer
            for i, layer in enumerate(self.layers):
                layer_weights = {}
                for etype in layer.rel_attns:
                    if etype in g.etypes:
                        src_type, _, dst_type = None, None, None
                        for src, e, dst in self.canonical_etypes:
                            if e == etype:
                                src_type, dst_type = src, dst
                                break

                        if (src_type and dst_type and
                            src_type in h_dict and dst_type in h_dict):
                            subg = g[src_type, etype, dst_type]
                            if subg.num_edges() > 0:
                                # Get attention weights (simplified)
                                with subg.local_scope():
                                    attn_module = layer.rel_attns[etype]
                                    q = attn_module.W_q(h_dict[dst_type]).view(-1, attn_module.num_heads, attn_module.head_dim)
                                    k = attn_module.W_k(h_dict[src_type]).view(-1, attn_module.num_heads, attn_module.head_dim)

                                    subg.srcdata['k'] = k
                                    subg.dstdata['q'] = q
                                    subg.apply_edges(attn_module._edge_attention)

                                    layer_weights[etype] = subg.edata['a'].detach()

                attention_weights[f'layer_{i}'] = layer_weights

        # Restore training mode
        if was_training:
            self.train()

        return attention_weights


class RHGNNAdapter(nn.Module):
    """Adapter to make RHGNN compatible with standard interface."""

    def __init__(
        self,
        in_feats: int,
        hidden: int,
        out_feats: int,
        dropout: float,
        input_dropout: float,
        num_layers: int,
        num_etypes: int,
        num_heads: int,
        target_ntype: str = "target",
    ):
        super().__init__()

        # Create canonical edge types for homogeneous graph
        canonical_etypes = [(target_ntype, f"rel_{i}", target_ntype) for i in range(num_etypes)]

        self.rhgnn = RHGNN(
            in_feats=in_feats,
            hidden=hidden,
            out_feats=out_feats,
            dropout=dropout,
            input_dropout=input_dropout,
            num_layers=num_layers,
            canonical_etypes=canonical_etypes,
            target_ntype=target_ntype,
            num_heads=num_heads,
        )

        self.target_ntype = target_ntype
        self.linear = nn.Linear(in_feats, in_feats)
        self.label_linear = nn.Linear(out_feats, in_feats)
        self.input_dropout = nn.Dropout(input_dropout)

    def forward(self, graph: dgl.DGLGraph, feat: torch.Tensor, label_feat: torch.Tensor) -> torch.Tensor:
        """Standard interface forward method."""
        # Combine node features and label features
        feat = self.linear(self.input_dropout(feat))
        label_feat = self.label_linear(self.input_dropout(label_feat))
        combined_feat = feat + label_feat

        # Convert homogeneous graph to heterogeneous format
        hetero_graph = self._convert_to_hetero(graph)
        feat_dict = {self.target_ntype: combined_feat}

        # Call RHGNN
        return self.rhgnn(hetero_graph, feat_dict)

    def _convert_to_hetero(self, graph: dgl.DGLGraph) -> dgl.DGLHeteroGraph:
        """Convert homogeneous graph to heterogeneous format."""
        # Create edge type mapping based on edge type data
        edge_types = graph.edata.get("_TYPE", torch.zeros(graph.num_edges(), dtype=torch.long))

        # Create heterogeneous graph data
        hetero_data = {}
        for etype_id in range(edge_types.max().item() + 1):
            mask = edge_types == etype_id
            if mask.sum() > 0:
                edges = graph.edges()
                src_nodes = edges[0][mask]
                dst_nodes = edges[1][mask]
                hetero_data[(self.target_ntype, f"rel_{etype_id}", self.target_ntype)] = (src_nodes, dst_nodes)

        # Create heterogeneous graph
        if hetero_data:
            hetero_graph = dgl.heterograph(hetero_data, num_nodes_dict={self.target_ntype: graph.num_nodes()})
        else:
            # Fallback: create self-loop edges
            nodes = torch.arange(graph.num_nodes())
            hetero_data[(self.target_ntype, "rel_0", self.target_ntype)] = (nodes, nodes)
            hetero_graph = dgl.heterograph(hetero_data, num_nodes_dict={self.target_ntype: graph.num_nodes()})

        return hetero_graph